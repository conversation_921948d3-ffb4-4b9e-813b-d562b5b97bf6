<?php
// Диагностика веб-интерфейса
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

// Initialize autoloader
$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;
use App\Models\Setting;
use App\Models\User;
use App\Controllers\AuthController;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Диагностика веб-интерфейса</h1>";

Database::init();
echo "<p>✅ База данных инициализирована</p>";

// Проверяем настройки
echo "<h2>📊 Настройки</h2>";
try {
    $miniappSettings = Setting::getFormattedByCategory('miniapp');
    echo "<p>Miniapp настроек: " . count($miniappSettings) . "</p>";
    
    $botSettings = Setting::getFormattedByCategory('bot');
    echo "<p>Bot настроек: " . count($botSettings) . "</p>";
    
    $nowpaymentsSettings = Setting::getFormattedByCategory('nowpayments');
    echo "<p>Nowpayments настроек: " . count($nowpaymentsSettings) . "</p>";
    
    if (count($miniappSettings) > 0) {
        echo "<h3>Пример настройки miniapp:</h3>";
        echo "<pre>" . print_r($miniappSettings[0], true) . "</pre>";
    }
    
    $stats = Setting::getStats();
    echo "<h3>Статистика настроек:</h3>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка настроек: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Проверяем пользователей
echo "<h2>👥 Пользователи</h2>";
try {
    $usersData = User::getUsers();
    echo "<p>Пользователей получено: " . count($usersData['users']) . "</p>";
    echo "<p>Всего в пагинации: " . $usersData['pagination']['total'] . "</p>";
    
    if (count($usersData['users']) > 0) {
        echo "<h3>Пример пользователя:</h3>";
        echo "<pre>" . print_r($usersData['users'][0], true) . "</pre>";
    }
    
    $userStats = User::getStats();
    echo "<h3>Статистика пользователей:</h3>";
    echo "<pre>" . print_r($userStats, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка пользователей: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Проверяем прямые SQL запросы
echo "<h2>🔍 Прямые SQL запросы</h2>";
try {
    $settingsCount = Database::query("SELECT COUNT(*) as cnt FROM settings")[0]['cnt'];
    echo "<p>Настроек в базе: $settingsCount</p>";

    $usersCount = Database::query("SELECT COUNT(*) as cnt FROM users")[0]['cnt'];
    echo "<p>Пользователей в базе: $usersCount</p>";

    $adminsCount = Database::query("SELECT COUNT(*) as cnt FROM admin_users")[0]['cnt'];
    echo "<p>Админов в базе: $adminsCount</p>";

} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка SQL: " . $e->getMessage() . "</p>";
}

// Проверяем аутентификацию
echo "<h2>🔐 Аутентификация</h2>";
try {
    $auth = new AuthController();
    $isLoggedIn = $auth->isLoggedIn();
    echo "<p>Авторизован: " . ($isLoggedIn ? 'ДА' : 'НЕТ') . "</p>";

    if ($isLoggedIn) {
        $currentUser = $auth->getCurrentUser();
        echo "<p>Текущий пользователь: " . ($currentUser ? $currentUser['username'] : 'не найден') . "</p>";
    }

    echo "<h3>Сессия:</h3>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";

} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка аутентификации: " . $e->getMessage() . "</p>";
}

// Проверяем файлы
echo "<h2>📁 Файлы</h2>";
$files = [
    'app/Views/admin/settings.php',
    'app/Views/admin/users.php',
    'app/Views/layouts/admin.php',
    'app/Controllers/AdminController.php',
    'database/app.sqlite'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    echo "<p>$file: " . ($exists ? "✅ ($size байт)" : "❌ НЕ НАЙДЕН") . "</p>";
}

echo "<h2>🎯 Рекомендации</h2>";
echo "<p>1. Проверьте, авторизованы ли вы в админке</p>";
echo "<p>2. Убедитесь, что данные есть в базе</p>";
echo "<p>3. Проверьте, что контроллеры правильно передают данные в представления</p>";

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
