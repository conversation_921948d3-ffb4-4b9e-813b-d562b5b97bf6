<?php
// Проверяем логи PHP
$logFile = ini_get('error_log');
if (!$logFile) {
    $logFile = 'php_errors.log';
}

echo "Проверяем логи в файле: $logFile\n\n";

if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $lines = explode("\n", $logs);
    
    // Показываем последние 20 строк
    $lastLines = array_slice($lines, -20);
    
    echo "Последние 20 строк логов:\n";
    echo "========================\n";
    foreach ($lastLines as $line) {
        if (!empty(trim($line))) {
            echo $line . "\n";
        }
    }
} else {
    echo "Файл логов не найден: $logFile\n";
    echo "Возможные места для логов:\n";
    echo "- " . __DIR__ . "/php_errors.log\n";
    echo "- " . $_SERVER['DOCUMENT_ROOT'] . "/php_errors.log\n";
    echo "- /var/log/php_errors.log\n";
}

// Также проверим настройки PHP для логирования
echo "\n\nНастройки логирования PHP:\n";
echo "log_errors: " . (ini_get('log_errors') ? 'On' : 'Off') . "\n";
echo "error_log: " . ini_get('error_log') . "\n";
echo "display_errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "\n";
?>
