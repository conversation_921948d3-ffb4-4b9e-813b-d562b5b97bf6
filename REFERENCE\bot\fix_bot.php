<?php
/**
 * fix_bot.php
 * Быстрое исправление проблем с ботом
 */

require_once __DIR__ . '/config.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Исправление бота</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";
echo "</head><body>\n";

echo "<h1>🔧 Быстрое исправление Telegram Bot</h1>\n";

// Функция для безопасного выполнения запросов
function safeTelegramRequest($method, $data = []) {
    try {
        return telegramRequest($method, $data);
    } catch (Exception $e) {
        botLog("EXCEPTION: {$method} - " . $e->getMessage());
        return false;
    }
}

// 1. Проверка базовой связи
echo "<h2>🔍 1. Проверка связи с Telegram</h2>\n";

$botInfo = safeTelegramRequest('getMe');
if ($botInfo) {
    echo "<p class='success'>✅ Связь с Telegram API работает</p>\n";
    echo "<p><strong>Bot Name:</strong> " . htmlspecialchars($botInfo['first_name']) . "</p>\n";
    echo "<p><strong>Username:</strong> @" . htmlspecialchars($botInfo['username']) . "</p>\n";
} else {
    echo "<p class='error'>❌ Нет связи с Telegram API</p>\n";
    echo "<p class='info'>💡 Возможные причины:</p>\n";
    echo "<ul>\n";
    echo "<li>Неверный BOT_TOKEN</li>\n";
    echo "<li>Проблемы с интернет-соединением</li>\n";
    echo "<li>Блокировка Telegram API</li>\n";
    echo "</ul>\n";
}

// 2. Исправление webhook
echo "<h2>🔗 2. Исправление Webhook</h2>\n";

// Сначала удаляем старый webhook
echo "<p>🗑️ Удаляем старый webhook...</p>\n";
$deleteResult = safeTelegramRequest('deleteWebhook', ['drop_pending_updates' => true]);

if ($deleteResult !== false) {
    echo "<p class='success'>✅ Старый webhook удален</p>\n";
} else {
    echo "<p class='warning'>⚠️ Не удалось удалить старый webhook (возможно, его не было)</p>\n";
}

// Ждем немного
sleep(2);

// Устанавливаем новый webhook
echo "<p>🔗 Устанавливаем новый webhook...</p>\n";
$webhookData = [
    'url' => WEBHOOK_URL,
    'allowed_updates' => ['message', 'callback_query'],
    'drop_pending_updates' => true
];

$setResult = safeTelegramRequest('setWebhook', $webhookData);

if ($setResult !== false) {
    echo "<p class='success'>✅ Новый webhook установлен</p>\n";
    echo "<p><strong>URL:</strong> " . WEBHOOK_URL . "</p>\n";
} else {
    echo "<p class='error'>❌ Не удалось установить webhook</p>\n";
}

// 3. Проверка webhook
echo "<h2>📋 3. Проверка состояния Webhook</h2>\n";

$webhookInfo = safeTelegramRequest('getWebhookInfo');
if ($webhookInfo) {
    if (!empty($webhookInfo['url'])) {
        echo "<p class='success'>✅ Webhook активен</p>\n";
        echo "<p><strong>URL:</strong> " . htmlspecialchars($webhookInfo['url']) . "</p>\n";
        echo "<p><strong>Pending Updates:</strong> " . ($webhookInfo['pending_update_count'] ?? 0) . "</p>\n";
        
        if (isset($webhookInfo['last_error_date'])) {
            $errorTime = date('Y-m-d H:i:s', $webhookInfo['last_error_date']);
            echo "<p class='warning'>⚠️ <strong>Последняя ошибка:</strong> {$errorTime}</p>\n";
            echo "<p class='warning'><strong>Сообщение:</strong> " . htmlspecialchars($webhookInfo['last_error_message']) . "</p>\n";
        } else {
            echo "<p class='success'>✅ Ошибок webhook нет</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Webhook не установлен</p>\n";
    }
}

// 4. Тест отправки сообщения
echo "<h2>📤 4. Тест отправки сообщения</h2>\n";

$testChatId = $_GET['chat_id'] ?? null;
if ($testChatId) {
    echo "<p>📤 Отправляем тестовое сообщение в chat_id: {$testChatId}</p>\n";
    
    $testMessage = "🧪 <b>Тест бота</b>\n\n";
    $testMessage .= "Время: " . date('Y-m-d H:i:s') . "\n";
    $testMessage .= "Статус: Бот работает корректно! ✅";
    
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '🚀 Запустить приложение',
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ]
        ]
    ];
    
    $sendResult = safeTelegramRequest('sendMessage', [
        'chat_id' => $testChatId,
        'text' => $testMessage,
        'parse_mode' => 'HTML',
        'reply_markup' => json_encode($keyboard)
    ]);
    
    if ($sendResult) {
        echo "<p class='success'>✅ Тестовое сообщение отправлено успешно!</p>\n";
    } else {
        echo "<p class='error'>❌ Не удалось отправить тестовое сообщение</p>\n";
    }
} else {
    echo "<p class='info'>💡 Для теста отправки добавьте параметр: <code>?chat_id=YOUR_CHAT_ID</code></p>\n";
    echo "<p>Пример: <code>fix_bot.php?chat_id=123456789</code></p>\n";
}

// 5. Очистка логов (опционально)
if (isset($_GET['clear_logs'])) {
    echo "<h2>🗑️ 5. Очистка логов</h2>\n";
    if (file_exists(LOG_FILE)) {
        file_put_contents(LOG_FILE, '');
        echo "<p class='success'>✅ Логи очищены</p>\n";
    } else {
        echo "<p class='warning'>⚠️ Файл логов не найден</p>\n";
    }
}

// 6. Итоговые рекомендации
echo "<h2>💡 6. Итоговые рекомендации</h2>\n";

if ($botInfo && $webhookInfo && !empty($webhookInfo['url'])) {
    echo "<p class='success'>🎉 <strong>Бот настроен корректно!</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ Связь с Telegram API работает</li>\n";
    echo "<li>✅ Webhook установлен и активен</li>\n";
    echo "<li>✅ Можно тестировать бота</li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>Следующие шаги:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Отправьте /start боту @" . BOT_USERNAME . "</li>\n";
    echo "<li>Проверьте работу всех функций</li>\n";
    echo "<li>При необходимости обновите картинку</li>\n";
    echo "</ol>\n";
} else {
    echo "<p class='error'>❌ <strong>Есть проблемы, требующие внимания</strong></p>\n";
    echo "<ul>\n";
    if (!$botInfo) echo "<li>❌ Проблемы с подключением к Telegram API</li>\n";
    if (!$webhookInfo || empty($webhookInfo['url'])) echo "<li>❌ Webhook не установлен</li>\n";
    echo "</ul>\n";
}

// 7. Быстрые действия
echo "<h2>⚡ 7. Быстрые действия</h2>\n";
echo "<p><a href='?'>🔄 Повторить диагностику</a></p>\n";
echo "<p><a href='?clear_logs=1'>🗑️ Очистить логи</a></p>\n";
echo "<p><a href='diagnose.php'>🔍 Подробная диагностика</a></p>\n";
echo "<p><a href='set_webhook.php'>🔗 Переустановить webhook</a></p>\n";

if ($testChatId) {
    echo "<p><a href='update_image.php?chat_id={$testChatId}'>🖼️ Обновить картинку</a></p>\n";
}

// Показываем последние логи
echo "<h2>📋 Последние логи</h2>\n";
if (file_exists(LOG_FILE)) {
    $logs = file_get_contents(LOG_FILE);
    $lastLogs = array_slice(explode("\n", trim($logs)), -5);
    if (!empty($lastLogs[0])) {
        echo "<pre style='background:#f5f5f5;padding:10px;border-radius:5px;'>" . htmlspecialchars(implode("\n", $lastLogs)) . "</pre>\n";
    } else {
        echo "<p class='info'>📝 Логи пусты</p>\n";
    }
} else {
    echo "<p class='warning'>⚠️ Файл логов не найден</p>\n";
}

echo "</body></html>\n";
?>
