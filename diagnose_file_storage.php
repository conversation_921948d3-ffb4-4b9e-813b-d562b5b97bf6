<?php
// Диагностика файлового хранения
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;
use App\Models\Setting;
use App\Models\User;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Диагностика файлового хранения</h1>";

// Проверяем константы
echo "<h2>⚙️ Константы</h2>";
echo "<p>DEV_MODE: " . (defined('DEV_MODE') ? (DEV_MODE ? 'TRUE ✅' : 'FALSE ❌') : 'НЕ ОПРЕДЕЛЕНО ❌') . "</p>";
echo "<p>DATABASE_PATH: " . (defined('DATABASE_PATH') ? DATABASE_PATH : 'НЕ ОПРЕДЕЛЕНО') . "</p>";

// Проверяем файлы
echo "<h2>📁 Файлы данных</h2>";
$dataFiles = [
    'database/settings.json',
    'database/admin_users.json', 
    'database/users.json'
];

foreach ($dataFiles as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    echo "<p>$file: " . ($exists ? "✅ ($size байт)" : "❌ НЕ НАЙДЕН") . "</p>";
    
    if ($exists && $size > 0) {
        $content = file_get_contents($file);
        $data = json_decode($content, true);
        if ($data) {
            echo "<p>&nbsp;&nbsp;Записей: " . count($data) . "</p>";
            if (count($data) > 0) {
                echo "<p>&nbsp;&nbsp;Первая запись: " . htmlspecialchars(substr(json_encode($data[0]), 0, 100)) . "...</p>";
            }
        } else {
            echo "<p>&nbsp;&nbsp;❌ Ошибка JSON</p>";
        }
    }
}

// Проверяем Database класс
echo "<h2>🔧 Database класс</h2>";
try {
    Database::init();
    echo "<p>✅ Database::init() выполнен</p>";
    
    // Проверяем, какой режим использует Database
    $reflection = new ReflectionClass('App\Core\Database');
    echo "<p>Database класс загружен</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка Database: " . $e->getMessage() . "</p>";
}

// Проверяем модели
echo "<h2>📊 Модели</h2>";

echo "<h3>Setting модель:</h3>";
try {
    $miniappSettings = Setting::getFormattedByCategory('miniapp');
    echo "<p>miniapp настроек: " . count($miniappSettings) . "</p>";
    
    $botSettings = Setting::getFormattedByCategory('bot');
    echo "<p>bot настроек: " . count($botSettings) . "</p>";
    
    if (count($miniappSettings) > 0) {
        echo "<p>Пример настройки:</p>";
        echo "<pre>" . print_r($miniappSettings[0], true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка Setting: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>User модель:</h3>";
try {
    $usersData = User::getUsers();
    echo "<p>Пользователей: " . count($usersData['users']) . "</p>";
    echo "<p>Всего в пагинации: " . $usersData['pagination']['total'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка User: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Создаем тестовые данные
echo "<h2>🧪 Создание тестовых данных</h2>";

// Создаем настройки
$settingsFile = 'database/settings.json';
$testSettings = [
    [
        'category' => 'miniapp',
        'key' => 'app_name',
        'value' => 'UniQPaid Test',
        'type' => 'string',
        'description' => 'Application name'
    ],
    [
        'category' => 'miniapp',
        'key' => 'conversion_rate',
        'value' => '1000',
        'type' => 'number',
        'description' => 'Conversion rate'
    ],
    [
        'category' => 'bot',
        'key' => 'telegram_bot_token',
        'value' => 'TEST_TOKEN_123',
        'type' => 'string',
        'description' => 'Bot token'
    ]
];

if (file_put_contents($settingsFile, json_encode($testSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    echo "<p>✅ Тестовые настройки созданы</p>";
} else {
    echo "<p style='color: red'>❌ Ошибка создания настроек</p>";
}

// Создаем админа
$adminsFile = 'database/admin_users.json';
$testAdmin = [
    [
        'id' => 1,
        'username' => 'admin',
        'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
        'email' => '<EMAIL>',
        'role' => 'admin',
        'is_active' => true,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]
];

if (file_put_contents($adminsFile, json_encode($testAdmin, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    echo "<p>✅ Тестовый админ создан</p>";
} else {
    echo "<p style='color: red'>❌ Ошибка создания админа</p>";
}

// Создаем пользователей
$usersFile = 'database/users.json';
$testUsers = [
    [
        'id' => 1,
        'telegram_id' => 123456789,
        'first_name' => 'Test',
        'last_name' => 'User',
        'username' => 'testuser',
        'language' => 'ru',
        'balance' => 1000,
        'created_at' => date('Y-m-d H:i:s')
    ]
];

if (file_put_contents($usersFile, json_encode($testUsers, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    echo "<p>✅ Тестовые пользователи созданы</p>";
} else {
    echo "<p style='color: red'>❌ Ошибка создания пользователей</p>";
}

echo "<h2>🔄 Повторная проверка моделей</h2>";

try {
    $miniappSettings = Setting::getFormattedByCategory('miniapp');
    echo "<p>miniapp настроек после создания: " . count($miniappSettings) . "</p>";
    
    $usersData = User::getUsers();
    echo "<p>Пользователей после создания: " . count($usersData['users']) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка повторной проверки: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/admin/?page=settings'>🔗 Настройки в админке</a></p>";
echo "<p><a href='/admin/?page=users'>🔗 Пользователи в админке</a></p>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
