<?php
// Отладка Database класса
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

// Initialize autoloader
$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Отладка Database класса</h1>";

Database::init();
echo "<p>✅ База данных инициализирована</p>";

// Проверяем подключение к базе
echo "<h2>📊 Проверка подключения</h2>";
try {
    $dbFile = 'database/app.sqlite';
    echo "<p>Файл базы: $dbFile</p>";
    echo "<p>Существует: " . (file_exists($dbFile) ? 'ДА' : 'НЕТ') . "</p>";
    echo "<p>Размер: " . filesize($dbFile) . " байт</p>";
    echo "<p>Права: " . substr(sprintf('%o', fileperms($dbFile)), -4) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка проверки файла: " . $e->getMessage() . "</p>";
}

// Тестируем простой SQL запрос
echo "<h2>🔍 Тест простого SQL</h2>";
try {
    $result = Database::query("SELECT 1 as test");
    echo "<p>Простой запрос: " . (is_array($result) ? 'РАБОТАЕТ' : 'НЕ РАБОТАЕТ') . "</p>";
    if (is_array($result)) {
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка простого запроса: " . $e->getMessage() . "</p>";
}

// Тестируем запрос к таблице settings
echo "<h2>📊 Тест таблицы settings</h2>";
try {
    $result = Database::query("SELECT COUNT(*) as count FROM settings");
    echo "<p>Результат COUNT settings:</p>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    if (is_array($result) && count($result) > 0) {
        echo "<p>Количество настроек: " . ($result[0]['count'] ?? 'НЕ НАЙДЕНО') . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка запроса settings: " . $e->getMessage() . "</p>";
}

// Тестируем запрос к таблице users
echo "<h2>👥 Тест таблицы users</h2>";
try {
    $result = Database::query("SELECT COUNT(*) as count FROM users");
    echo "<p>Результат COUNT users:</p>";
    echo "<pre>" . print_r($result, true) . "</pre>";
    
    if (is_array($result) && count($result) > 0) {
        echo "<p>Количество пользователей: " . ($result[0]['count'] ?? 'НЕ НАЙДЕНО') . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка запроса users: " . $e->getMessage() . "</p>";
}

// Тестируем запрос к таблице admin_users
echo "<h2>🔐 Тест таблицы admin_users</h2>";
try {
    $result = Database::query("SELECT * FROM admin_users");
    echo "<p>Результат admin_users:</p>";
    echo "<pre>" . print_r($result, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка запроса admin_users: " . $e->getMessage() . "</p>";
}

// Проверяем список таблиц
echo "<h2>📋 Список таблиц</h2>";
try {
    $result = Database::query("SELECT name FROM sqlite_master WHERE type='table'");
    echo "<p>Таблицы в базе:</p>";
    echo "<pre>" . print_r($result, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка получения списка таблиц: " . $e->getMessage() . "</p>";
}

// Проверяем метод queryOne
echo "<h2>🎯 Тест queryOne</h2>";
try {
    $result = Database::queryOne("SELECT COUNT(*) as count FROM settings");
    echo "<p>Результат queryOne settings:</p>";
    echo "<pre>" . print_r($result, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка queryOne: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
echo "<p><a href='/diagnose_web_interface.php'>← Диагностика интерфейса</a></p>";
?>
