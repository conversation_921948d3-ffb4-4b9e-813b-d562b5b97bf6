<?php
/**
 * webhook.php
 * Основной файл обработки webhook от Telegram.
 * 
 * ВЕРСИЯ 3.0 (ФИНАЛЬНАЯ, ИСПРАВЛЕННАЯ)
 * - ИСПРАВЛЕНА критическая ошибка в логике fallback-локализации в функции getText().
 * - Добавлена защита от отправки пустых сообщений в sendMessage/sendPhoto.
 * - Внедрена централизованная функция getText() для локализации.
 * - Полностью устранены дублирования текстовых строк (принцип DRY).
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../api/db_mock.php';

// =========================================================================
// --- СИСТЕМА ЛОКАЛИЗАЦИИ (ЕДИНАЯ ТОЧКА УПРАВЛЕНИЯ ТЕКСТАМИ) ---
// =========================================================================

function loadBotTextsFromFile() {
    static $texts = null;
    if ($texts !== null) return $texts;

    $textsFile = __DIR__ . '/bot_texts.json';
    if (!file_exists($textsFile)) {
        botLog("ERROR: Файл переводов не найден: " . $textsFile);
        return $texts = false;
    }

    $content = file_get_contents($textsFile);
    $decoded = json_decode($content, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        botLog("ERROR: Ошибка декодирования JSON в файле bot_texts.json: " . json_last_error_msg());
        return $texts = false;
    }
    
    botLog("DEBUG: Переводы успешно загружены. Языки: " . implode(', ', array_keys($decoded)));
    return $texts = $decoded;
}

function getNestedValue($array, $key) {
    if (!is_array($array)) return null;
    $keys = explode('.', $key);
    $value = $array;
    foreach ($keys as $k) {
        if (!isset($value[$k])) return null;
        $value = $value[$k];
    }
    return $value;
}

function getBotTextFromFile($key, $lang, $params = []) {
    $texts = loadBotTextsFromFile();
    if (!$texts || !isset($texts[$lang])) return null;
    
    $value = getNestedValue($texts[$lang], $key);
    if ($value === null) return null;

    if (!empty($params) && is_string($value)) {
        foreach ($params as $param => $replacement) {
            $value = str_replace('{' . $param . '}', $replacement, $value);
        }
    }
    return $value;
}

function detectUserLanguage($telegramUser) {
    $baseLanguage = substr($telegramUser['language_code'] ?? 'en', 0, 2);
    $russianLanguages = ['ru', 'be', 'uk', 'kk', 'ky', 'uz', 'tg', 'az', 'hy', 'ka', 'ro'];
    return in_array($baseLanguage, $russianLanguages) ? 'ru' : 'en';
}

function getText($key, $telegramUser, $params = []) {
    $lang = detectUserLanguage($telegramUser);

    $text = getBotTextFromFile($key, $lang, $params);
    if ($text !== null) return $text;

    if ($lang !== 'en') {
        botLog("WARNING: Текст для ключа '{$key}' на языке '{$lang}' не найден. Пробуем 'en'.");
        // --- ИСПРАВЛЕНО: Правильный порядок аргументов ($key, 'en') ---
        $text = getBotTextFromFile($key, 'en', $params);
        if ($text !== null) return $text;
    }

    botLog("ERROR: Текст для ключа '{$key}' не найден ни на одном языке. Возвращаем ключ для отладки.");
    return $key;
}

// =========================================================================
// --- ОСНОВНАЯ ЛОГИКА ОБРАБОТКИ WEBHOOK ---
// =========================================================================

$input = file_get_contents('php://input');
$update = json_decode($input, true);

if (!$update) {
    botLog("ERROR: Не удалось декодировать JSON от Telegram");
    exit;
}

botLog("INFO: Получен update: " . json_encode($update, JSON_UNESCAPED_UNICODE));

if (isset($update['message'])) {
    handleMessage($update['message']);
} elseif (isset($update['callback_query'])) {
    handleCallbackQuery($update['callback_query']);
}

function handleMessage($message) {
    $chatId = $message['chat']['id'];
    $telegramUser = $message['from'];
    $text = $message['text'] ?? '';
    $firstName = $telegramUser['first_name'] ?? getText('system.user_fallback', $telegramUser);

    botLog("INFO: Сообщение от пользователя {$telegramUser['id']} ({$firstName}): {$text}");

    if (strpos($text, '/start') === 0) {
        handleStartCommand($chatId, $text, $message);
        return;
    }

    switch ($text) {
        case '/help': handleHelpCommand($chatId, $telegramUser); break;
        case '/balance': handleBalanceCommand($chatId, $telegramUser); break;
        case '/stats': handleStatsCommand($chatId, $telegramUser); break;
        default: showMainMenu($chatId, $telegramUser); break;
    }
}

function handleCallbackQuery($callbackQuery) {
    $chatId = $callbackQuery['message']['chat']['id'];
    $telegramUser = $callbackQuery['from'];
    $data = $callbackQuery['data'];

    botLog("INFO: Callback query от {$telegramUser['id']}: {$data}");
    telegramRequest('answerCallbackQuery', ['callback_query_id' => $callbackQuery['id']]);

    switch ($data) {
        case 'my_balance': handleBalanceCommand($chatId, $telegramUser); break;
        case 'invite_friends': handleInviteFriends($chatId, $telegramUser); break;
        case 'my_stats': handleStatsCommand($chatId, $telegramUser); break;
        case 'help': handleHelpCommand($chatId, $telegramUser); break;
        case 'detailed_info': handleDetailedInfo($chatId, $telegramUser); break;
        case 'how_it_works': handleHowItWorks($chatId, $telegramUser); break;
        case 'main_menu': showMainMenu($chatId, $telegramUser); break;
        default: sendMessage($chatId, getText('system.unknown_command', $telegramUser)); break;
    }
}

// =========================================================================
// --- РАБОТА С ДАННЫМИ ПОЛЬЗОВАТЕЛЯ ---
// =========================================================================

function registerUser($telegramUser, $referrerId = null) {
    $userData = loadUserData();
    $userId = $telegramUser['id'];
    $isNewUser = !isset($userData[$userId]);
    $userLanguage = detectUserLanguage($telegramUser);

    if ($isNewUser) {
        $userData[$userId] = [
            'balance' => 0, 'total_earned' => 0, 'withdrawals' => [], 'withdrawal_log' => [],
            'referrer_id' => $referrerId, 'referrals' => [], 'referral_earnings' => 0,
            'first_name' => $telegramUser['first_name'] ?? '', 'last_name' => $telegramUser['last_name'] ?? '',
            'username' => $telegramUser['username'] ?? '', 'language' => $userLanguage,
            'registered_at' => time(), 'last_activity' => time(),
            'suspicious_activity_count' => 0, 'withdrawals_count' => 0
        ];
        botLog("INFO: Зарегистрирован новый пользователь {$userId} ({$userData[$userId]['first_name']}) с языком {$userLanguage}");
        
        if ($referrerId && isset($userData[$referrerId])) {
            $userData[$referrerId]['referrals'][] = ['user_id' => $userId, 'registered_at' => time()];
            botLog("INFO: Пользователь {$userId} добавлен как реферал к {$referrerId}");
        }
    } else {
        $userData[$userId]['first_name'] = $telegramUser['first_name'] ?? $userData[$userId]['first_name'];
        $userData[$userId]['last_name'] = $telegramUser['last_name'] ?? $userData[$userId]['last_name'];
        $userData[$userId]['username'] = $telegramUser['username'] ?? $userData[$userId]['username'];
        $userData[$userId]['last_activity'] = time();
        $userData[$userId]['language'] = $userLanguage;
    }

    saveUserData($userData);
    return $isNewUser;
}

// =========================================================================
// --- ОБРАБОТЧИКИ КОМАНД И ДЕЙСТВИЙ ---
// =========================================================================

function handleStartCommand($chatId, $text, $message) {
    $telegramUser = $message['from'];
    
    $referrerId = null;
    if (preg_match('/\/start\s+(\d+)/', $text, $matches)) {
        $referrerId = (int)$matches[1];
    }
    registerUser($telegramUser, $referrerId);

    $welcomeText = "<b>" . getText('welcome.title', $telegramUser) . "</b>\n\n"
        . "<b>" . getText('welcome.subtitle', $telegramUser) . "</b>\n"
        . "<b>" . getText('welcome.description', $telegramUser) . "</b>\n\n"
        . getText('welcome.ad_info', $telegramUser) . "\n\n"
        . getText('welcome.warning', $telegramUser) . "\n\n"
        . "<b>" . getText('welcome.how_works_title', $telegramUser) . "</b>\n"
        . getText('welcome.earn_coins', $telegramUser) . "\n"
        . getText('welcome.invite_friends', $telegramUser) . "\n"
        . getText('welcome.withdraw_crypto', $telegramUser) . "\n\n"
        . getText('welcome.exchange_rate', $telegramUser) . "\n\n"
        . "<b>" . getText('welcome.start_earning', $telegramUser) . "</b>";

    $keyboard = ['inline_keyboard' => [
        [['text' => getText('buttons.launch_app', $telegramUser), 'web_app' => ['url' => WEBAPP_URL]]],
        [['text' => getText('buttons.detailed_info', $telegramUser), 'callback_data' => 'detailed_info'], ['text' => getText('buttons.how_it_works', $telegramUser), 'callback_data' => 'how_it_works']],
        [['text' => getText('buttons.my_balance', $telegramUser), 'callback_data' => 'my_balance'], ['text' => getText('buttons.friends', $telegramUser), 'callback_data' => 'invite_friends']],
        [['text' => getText('buttons.statistics', $telegramUser), 'callback_data' => 'my_stats'], ['text' => getText('buttons.help', $telegramUser), 'callback_data' => 'help']]
    ]];
    
    $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?v=' . time();
    if (!sendPhoto($chatId, $logoUrl, $welcomeText, $keyboard)) {
        botLog("WARNING: Не удалось отправить картинку, отправляем текст.");
        sendMessage($chatId, $welcomeText, $keyboard);
    }
}

function showMainMenu($chatId, $telegramUser) {
    $firstName = $telegramUser['first_name'] ?? getText('system.user_fallback', $telegramUser);
    $text = getText('main_menu.hello', $telegramUser, ['name' => $firstName]) . "\n\n" . getText('main_menu.choose_action', $telegramUser);
    $keyboard = ['inline_keyboard' => [
        [['text' => getText('buttons.open_app', $telegramUser), 'web_app' => ['url' => WEBAPP_URL]]],
        [['text' => getText('buttons.balance', $telegramUser), 'callback_data' => 'my_balance'],['text' => getText('buttons.friends', $telegramUser), 'callback_data' => 'invite_friends']],
        [['text' => getText('buttons.statistics', $telegramUser), 'callback_data' => 'my_stats'],['text' => getText('buttons.help', $telegramUser), 'callback_data' => 'help']]
    ]];
    sendMessage($chatId, $text, $keyboard);
}

function handleBalanceCommand($chatId, $telegramUser) {
    $userData = loadUserData(); $userId = $telegramUser['id'];
    if (!isset($userData[$userId])) { sendMessage($chatId, getText('common.user_not_found', $telegramUser)); return; }
    $user = $userData[$userId];
    $balance = $user['balance'] ?? 0;
    $usdAmount = $balance * COIN_VALUE_USD;
    $formattedUsd = rtrim(rtrim(number_format($usdAmount, 3, '.', ''), '0'), '.');
    $text = "<b>" . getText('balance.title', $telegramUser) . "</b>\n\n"
        . getText('balance.current_balance', $telegramUser, ['balance' => $balance]) . "\n"
        . getText('balance.balance_usd', $telegramUser, ['amount' => $formattedUsd]) . "\n\n"
        . getText('balance.total_earned', $telegramUser, ['amount' => $user['total_earned'] ?? 0]) . "\n"
        . getText('balance.from_referrals', $telegramUser, ['amount' => $user['referral_earnings'] ?? 0]) . "\n"
        . getText('balance.withdrawals_count', $telegramUser, ['count' => $user['withdrawals_count'] ?? 0]) . "\n\n"
        . "<b>" . getText('balance.open_app_withdraw', $telegramUser) . "</b>";
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.open_app', $telegramUser), 'web_app' => ['url' => WEBAPP_URL]]],[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

function handleInviteFriends($chatId, $telegramUser) {
    $userId = $telegramUser['id']; $userData = loadUserData();
    if (!isset($userData[$userId])) { sendMessage($chatId, getText('common.user_not_found', $telegramUser)); return; }
    $user = $userData[$userId];
    $referralLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";
    $text = "<b>" . getText('referrals.program_title', $telegramUser) . "</b>\n\n"
        . getText('referrals.bonus_text', $telegramUser, ['percent' => REFERRAL_BONUS_PERCENT]) . "\n\n"
        . "<b>" . getText('referrals.stats_title', $telegramUser) . "</b>\n"
        . getText('referrals.invited_friends', $telegramUser, ['count' => count($user['referrals'] ?? [])]) . "\n"
        . getText('referrals.earned_from_referrals', $telegramUser, ['amount' => $user['referral_earnings'] ?? 0]) . "\n\n"
        . "<b>" . getText('referrals.your_referral_link', $telegramUser) . "</b>\n<code>{$referralLink}</code>\n\n"
        . getText('referrals.share_with_friends', $telegramUser);
    $shareMessage = getText('referrals.share_text', $telegramUser);
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.share_link', $telegramUser), 'switch_inline_query' => "{$shareMessage} {$referralLink}"]],[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

function handleStatsCommand($chatId, $telegramUser) {
    $userData = loadUserData(); $userId = $telegramUser['id'];
    if (!isset($userData[$userId])) { sendMessage($chatId, getText('common.user_not_found', $telegramUser)); return; }
    $user = $userData[$userId];
    $registeredAt = $user['registered_at'] ?? time();
    $daysActive = max(1, floor((time() - $registeredAt) / 86400));
    $avgPerDay = round(($user['total_earned'] ?? 0) / $daysActive, 2);
    $text = "<b>" . getText('statistics.title', $telegramUser) . "</b>\n\n"
        . getText('statistics.current_balance', $telegramUser, ['balance' => $user['balance'] ?? 0]) . "\n"
        . getText('statistics.total_earned', $telegramUser, ['amount' => $user['total_earned'] ?? 0]) . "\n"
        . getText('statistics.from_referrals', $telegramUser, ['amount' => $user['referral_earnings'] ?? 0]) . "\n"
        . getText('statistics.withdrawals_count', $telegramUser, ['count' => $user['withdrawals_count'] ?? 0]) . "\n\n"
        . getText('statistics.days_active', $telegramUser, ['days' => $daysActive]) . "\n"
        . getText('statistics.average_per_day', $telegramUser, ['amount' => $avgPerDay]) . "\n"
        . getText('statistics.invited_friends', $telegramUser, ['count' => count($user['referrals'] ?? [])]) . "\n\n"
        . getText('statistics.registration_date', $telegramUser, ['date' => date('d.m.Y', $registeredAt)]);
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.open_app', $telegramUser), 'web_app' => ['url' => WEBAPP_URL]]],[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

function handleHelpCommand($chatId, $telegramUser) {
    $text = "<b>" . getText('help.title', $telegramUser) . "</b>\n\n"
        . getText('help.description', $telegramUser) . "\n\n"
        . "<b>" . getText('help.how_to_use', $telegramUser) . "</b>\n"
        . getText('help.step1', $telegramUser) . "\n" . getText('help.step2', $telegramUser) . "\n"
        . getText('help.step3', $telegramUser) . "\n" . getText('help.step4', $telegramUser) . "\n\n"
        . getText('help.rate_info', $telegramUser) . "\n" . getText('help.referral_bonus', $telegramUser) . "\n"
        . getText('help.instant_payouts', $telegramUser);
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.open_app', $telegramUser), 'web_app' => ['url' => WEBAPP_URL]]],[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

function handleDetailedInfo($chatId, $telegramUser) {
    $text = "<b>" . getText('detailed_info.title', $telegramUser) . "</b>\n\n"
        . "<b>" . getText('detailed_info.limitations_title', $telegramUser) . "</b>\n"
        . getText('detailed_info.limitations_links', $telegramUser) . "\n"
        . getText('detailed_info.limitations_banners', $telegramUser) . "\n"
        . getText('detailed_info.limitations_videos', $telegramUser) . "\n\n"
        . "<b>" . getText('detailed_info.rewards_title', $telegramUser) . "</b>\n"
        . getText('detailed_info.rewards_banner', $telegramUser) . "\n"
        . getText('detailed_info.rewards_video', $telegramUser) . "\n"
        . getText('detailed_info.rewards_link', $telegramUser) . "\n\n"
        . "<b>" . getText('detailed_info.referral_title', $telegramUser) . "</b>\n"
        . getText('detailed_info.referral_description', $telegramUser) . "\n\n"
        . "⚠️ " . getText('detailed_info.blockchain_warning_text', $telegramUser) . "\n"
        . getText('detailed_info.blockchain_warning_docs', $telegramUser);
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

function handleHowItWorks($chatId, $telegramUser) {
    $text = "<b>" . getText('how_it_works.title', $telegramUser) . "</b>\n\n"
        . "<b>" . getText('how_it_works.step1_title', $telegramUser) . "</b>\n"
        . getText('how_it_works.step1_start', $telegramUser) . "\n" . getText('how_it_works.step1_auto', $telegramUser) . "\n" . getText('how_it_works.step1_balance', $telegramUser) . "\n\n"
        . "<b>" . getText('how_it_works.step2_title', $telegramUser) . "</b>\n"
        . getText('how_it_works.step2_open', $telegramUser) . "\n" . getText('how_it_works.step2_choose', $telegramUser) . "\n"
        . getText('how_it_works.step2_push', $telegramUser) . "\n" . getText('how_it_works.step2_watch', $telegramUser) . "\n" . getText('how_it_works.step2_tap', $telegramUser) . "\n\n"
        . "<b>" . getText('how_it_works.step3_title', $telegramUser) . "</b>\n"
        . getText('how_it_works.step3_referral', $telegramUser) . "\n" . getText('how_it_works.step3_share', $telegramUser) . "\n" . getText('how_it_works.step3_bonus', $telegramUser) . "\n\n"
        . "<b>" . getText('how_it_works.step4_title', $telegramUser) . "</b>\n"
        . getText('how_it_works.step4_minimum', $telegramUser) . "\n" . getText('how_it_works.step4_crypto', $telegramUser) . "\n" . getText('how_it_works.step4_instant', $telegramUser) . "\n\n"
        . getText('how_it_works.exchange_rate', $telegramUser) . "\n\n"
        . "<b>" . getText('how_it_works.call_to_action', $telegramUser) . "</b>";
    $keyboard = ['inline_keyboard' => [[['text' => getText('buttons.back', $telegramUser), 'callback_data' => 'main_menu']]]];
    sendMessage($chatId, $text, $keyboard);
}

// Переопределим функции отправки для добавления защиты от пустых сообщений
// Убедитесь, что эти функции находятся ПОСЛЕ всех их вызовов или в `config.php`
function sendMessage($chatId, $text, $keyboard = null) {
    if (empty($text)) {
        botLog("FATAL: Попытка отправить пустое сообщение в чат {$chatId}. Отменено.");
        return false;
    }
    $params = ['chat_id' => $chatId, 'text' => $text, 'parse_mode' => 'HTML'];
    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }
    return telegramRequest('sendMessage', $params);
}

function sendPhoto($chatId, $photoUrl, $caption = '', $keyboard = null) {
    if (empty($caption)) {
        botLog("FATAL: Попытка отправить фото с пустым описанием в чат {$chatId}. Отменено.");
        return false;
    }
    $params = ['chat_id' => $chatId, 'photo' => $photoUrl, 'caption' => $caption, 'parse_mode' => 'HTML'];
    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }
    return telegramRequest('sendPhoto', $params);
}
?>