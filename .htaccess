# UniQPaid Application .htaccess
# Simple configuration for static files and admin access

RewriteEngine On

# Allow direct access to existing files and directories
# No rewriting needed for this simple structure

# Security: Deny access to sensitive files and directories
<Files "*.php~">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Deny access to app directory
<IfModule mod_rewrite.c>
    RewriteRule ^app/ - [F,L]
</IfModule>

# Deny access to database directory
<IfModule mod_rewrite.c>
    RewriteRule ^database/ - [F,L]
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
