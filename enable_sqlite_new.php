<?php
echo "=== ВКЛЮЧЕНИЕ SQLITE В НОВОМ OSPANEL ===\n\n";

// Находим путь к PHP
$phpPath = PHP_BINARY;
$phpDir = dirname($phpPath);
$phpIniPath = php_ini_loaded_file();

echo "PHP путь: $phpPath\n";
echo "PHP директория: $phpDir\n";
echo "PHP.ini файл: $phpIniPath\n\n";

// Проверяем текущие расширения
echo "🔧 ТЕКУЩИЕ РАСШИРЕНИЯ:\n";
echo "PDO: " . (extension_loaded('pdo') ? 'YES' : 'NO') . "\n";
echo "PDO SQLite: " . (extension_loaded('pdo_sqlite') ? 'YES' : 'NO') . "\n";
echo "SQLite3: " . (extension_loaded('sqlite3') ? 'YES' : 'NO') . "\n\n";

// Ищем файлы расширений
echo "📁 ПОИСК ФАЙЛОВ РАСШИРЕНИЙ:\n";
$extDir = $phpDir . DIRECTORY_SEPARATOR . 'ext';
echo "Папка расширений: $extDir\n";

if (is_dir($extDir)) {
    $sqliteFiles = [
        'php_pdo_sqlite.dll',
        'php_sqlite3.dll'
    ];
    
    foreach ($sqliteFiles as $file) {
        $filePath = $extDir . DIRECTORY_SEPARATOR . $file;
        if (file_exists($filePath)) {
            $size = filesize($filePath);
            $date = date('Y-m-d H:i:s', filemtime($filePath));
            echo "✅ $file ($size байт, $date)\n";
        } else {
            echo "❌ $file НЕ НАЙДЕН\n";
        }
    }
} else {
    echo "❌ Папка расширений не найдена: $extDir\n";
}

// Читаем и исправляем php.ini
echo "\n📝 ИСПРАВЛЕНИЕ PHP.INI:\n";

if ($phpIniPath && file_exists($phpIniPath)) {
    echo "Читаем php.ini: $phpIniPath\n";
    
    $content = file_get_contents($phpIniPath);
    $originalContent = $content;
    
    // Создаем резервную копию
    $backupPath = $phpIniPath . '.backup_sqlite_' . date('Y-m-d_H-i-s');
    file_put_contents($backupPath, $content);
    echo "✅ Резервная копия: $backupPath\n";
    
    $changes = 0;
    
    // Включаем расширения SQLite
    $extensions = [
        'pdo_sqlite' => 'extension=pdo_sqlite',
        'sqlite3' => 'extension=sqlite3'
    ];
    
    foreach ($extensions as $ext => $line) {
        // Ищем закомментированную строку
        $commented = ";$line";
        if (strpos($content, $commented) !== false) {
            $content = str_replace($commented, $line, $content);
            echo "✅ Включено расширение: $ext\n";
            $changes++;
        }
        // Если уже включено
        elseif (strpos($content, $line) !== false) {
            echo "✅ Расширение уже включено: $ext\n";
        }
        // Если строки нет вообще, добавляем
        else {
            // Ищем секцию Dynamic Extensions
            $dynamicPos = strpos($content, '; Dynamic Extensions ;');
            if ($dynamicPos !== false) {
                $insertPos = strpos($content, "\n", $dynamicPos) + 1;
                $content = substr_replace($content, "$line\n", $insertPos, 0);
                echo "✅ Добавлено расширение: $ext\n";
                $changes++;
            }
        }
    }
    
    // Проверяем extension_dir
    if (strpos($content, 'extension_dir') !== false) {
        // Ищем строку extension_dir
        if (preg_match('/^;?extension_dir\s*=\s*"?([^"\n\r]*)"?/m', $content, $matches)) {
            $currentExtDir = trim($matches[1], '"');
            echo "Текущий extension_dir: $currentExtDir\n";
            
            if ($currentExtDir !== $extDir && $currentExtDir !== 'ext') {
                // Исправляем путь
                $content = preg_replace('/^;?extension_dir\s*=.*$/m', 'extension_dir = "' . $extDir . '"', $content);
                echo "✅ Исправлен extension_dir\n";
                $changes++;
            }
        }
    }
    
    // Сохраняем изменения
    if ($changes > 0) {
        if (file_put_contents($phpIniPath, $content)) {
            echo "✅ php.ini сохранен с $changes изменениями\n";
        } else {
            echo "❌ Ошибка сохранения php.ini\n";
        }
    } else {
        echo "ℹ️ Изменения в php.ini не требуются\n";
    }
    
} else {
    echo "❌ php.ini не найден или недоступен\n";
}

echo "\n🎯 РЕЗУЛЬТАТ:\n";
echo "✅ Поиск и настройка SQLite завершены\n";
echo "🔄 ПЕРЕЗАПУСТИТЕ ВЕБА-СЕРВЕР для применения изменений\n";
echo "\nПосле перезапуска запустите тест SQLite снова\n";

echo "\n=== КОНЕЦ НАСТРОЙКИ ===\n";
?>
