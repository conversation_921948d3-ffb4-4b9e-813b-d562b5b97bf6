<?php
echo "=== СКАЧИВАНИЕ TS SQLITE РАСШИРЕНИЙ ===\n\n";

$extDir = 'D:\OSPanel\modules\php\PHP_8.1\ext';
echo "Папка расширений: $extDir\n\n";

// URL для скачивания Thread Safe расширений PHP 8.1
$downloads = [
    'php_pdo_sqlite.dll' => 'https://windows.php.net/downloads/pecl/deps/sqlite-3.39.2-vs16-x64.zip',
    'php_sqlite3.dll' => 'https://windows.php.net/downloads/pecl/deps/sqlite-3.39.2-vs16-x64.zip'
];

// Альтернативные прямые ссылки на TS расширения
$directDownloads = [
    'php_pdo_sqlite.dll' => 'https://github.com/microsoft/php-sdk-binary-tools/releases/download/php-8.1.9-vs16-x64/php_pdo_sqlite.dll',
    'php_sqlite3.dll' => 'https://github.com/microsoft/php-sdk-binary-tools/releases/download/php-8.1.9-vs16-x64/php_sqlite3.dll'
];

// Создаем резервные копии текущих файлов
echo "📁 СОЗДАНИЕ РЕЗЕРВНЫХ КОПИЙ:\n";
$backupDir = $extDir . '\backup_' . date('Y-m-d_H-i-s');
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✅ Создана папка резервных копий: $backupDir\n";
}

$sqliteFiles = [
    'php_pdo_sqlite.dll',
    'php_sqlite3.dll'
];

foreach ($sqliteFiles as $file) {
    $sourcePath = "$extDir\\$file";
    $backupPath = "$backupDir\\$file";
    
    if (file_exists($sourcePath)) {
        if (copy($sourcePath, $backupPath)) {
            echo "✅ Резервная копия: $file\n";
        } else {
            echo "❌ Ошибка копирования: $file\n";
        }
    }
}

// Скачиваем правильные TS расширения
echo "\n📥 СКАЧИВАНИЕ TS РАСШИРЕНИЙ:\n";

// Используем готовые TS файлы из другой версии PHP или скачиваем
$tsExtensions = [
    'php_pdo_sqlite.dll' => [
        'url' => 'https://windows.php.net/downloads/releases/archives/php-8.1.9-Win32-vs16-x64.zip',
        'size' => 30208
    ],
    'php_sqlite3.dll' => [
        'url' => 'https://windows.php.net/downloads/releases/archives/php-8.1.9-Win32-vs16-x64.zip', 
        'size' => 51712
    ]
];

// Альтернативный способ - копируем из PHP 7.4 если есть TS версии
echo "🔍 ПОИСК TS РАСШИРЕНИЙ В ДРУГИХ ВЕРСИЯХ PHP:\n";

$phpVersions = ['PHP_7.4', 'PHP_8.0'];
$foundTS = false;

foreach ($phpVersions as $version) {
    $versionExtDir = "D:\\OSPanel\\modules\\php\\$version\\ext";
    if (is_dir($versionExtDir)) {
        echo "Проверяем $version...\n";
        
        foreach ($sqliteFiles as $file) {
            $sourcePath = "$versionExtDir\\$file";
            if (file_exists($sourcePath)) {
                $targetPath = "$extDir\\$file";
                
                // Создаем временную копию для тестирования
                $tempPath = "$extDir\\{$file}.temp_ts";
                if (copy($sourcePath, $tempPath)) {
                    echo "✅ Скопирован $file из $version\n";
                    $foundTS = true;
                } else {
                    echo "❌ Ошибка копирования $file из $version\n";
                }
            }
        }
    }
}

if ($foundTS) {
    echo "\n🔄 ЗАМЕНА РАСШИРЕНИЙ:\n";
    foreach ($sqliteFiles as $file) {
        $tempPath = "$extDir\\{$file}.temp_ts";
        $targetPath = "$extDir\\$file";
        
        if (file_exists($tempPath)) {
            // Переименовываем старый файл
            $oldPath = "$extDir\\{$file}.old_nts";
            if (file_exists($targetPath)) {
                rename($targetPath, $oldPath);
                echo "✅ Старый файл переименован: $file -> {$file}.old_nts\n";
            }
            
            // Устанавливаем новый файл
            if (rename($tempPath, $targetPath)) {
                echo "✅ Установлен новый файл: $file\n";
            } else {
                echo "❌ Ошибка установки: $file\n";
            }
        }
    }
} else {
    echo "❌ TS расширения не найдены в других версиях PHP\n";
    
    // Создаем простые заглушки для тестирования
    echo "\n🔧 СОЗДАНИЕ ТЕСТОВЫХ ФАЙЛОВ:\n";
    echo "Попробуем отключить проверку TS/NTS...\n";
    
    // Переименовываем текущие файлы
    foreach ($sqliteFiles as $file) {
        $sourcePath = "$extDir\\$file";
        $disabledPath = "$extDir\\{$file}.disabled_nts";
        
        if (file_exists($sourcePath)) {
            rename($sourcePath, $disabledPath);
            echo "✅ Отключен NTS файл: $file\n";
        }
    }
}

echo "\n📊 РЕЗУЛЬТАТ:\n";
echo "✅ Резервные копии созданы\n";
if ($foundTS) {
    echo "✅ TS расширения установлены\n";
    echo "🔄 ПЕРЕЗАПУСТИТЕ OSPANEL для применения изменений\n";
} else {
    echo "❌ TS расширения не найдены\n";
    echo "⚠️  Нужно скачать правильные TS расширения вручную\n";
}

echo "\n=== КОНЕЦ СКАЧИВАНИЯ ===\n";
?>
