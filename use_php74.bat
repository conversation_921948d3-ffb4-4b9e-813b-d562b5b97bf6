@echo off
echo Переключение на PHP 7.4 (с SQLite)...
set PATH=D:\OSPanel\modules\php\PHP_7.4;%PATH%
echo ✅ PHP 7.4 активирован
php -v
echo.
echo Проверка SQLite:
php -r "echo 'PDO SQLite: ' . (extension_loaded('pdo_sqlite') ? 'YES' : 'NO') . PHP_EOL;"
echo.
echo Для постоянного переключения выполните:
echo [Environment]::SetEnvironmentVariable('PATH', 'D:\OSPanel\modules\php\PHP_7.4;' + [Environment]::GetEnvironmentVariable('PATH', 'User'), 'User')
pause