<?php
echo "=== ВОССТАНОВЛЕНИЕ ДАННЫХ ===\n\n";

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

Database::init();

echo "✅ База данных инициализирована\n\n";

// 1. Восстанавливаем настройки из резервной копии
echo "📊 ВОССТАНОВЛЕНИЕ НАСТРОЕК:\n";
$backupDirs = glob('database/backup_json_*');
if (count($backupDirs) > 0) {
    $latestBackup = end($backupDirs);
    $settingsBackup = $latestBackup . '/settings.json';
    
    if (file_exists($settingsBackup)) {
        echo "Найдена резервная копия: $settingsBackup\n";
        
        $jsonData = json_decode(file_get_contents($settingsBackup), true);
        if ($jsonData && is_array($jsonData)) {
            echo "Найдено настроек в резервной копии: " . count($jsonData) . "\n";
            
            $imported = 0;
            foreach ($jsonData as $setting) {
                if (isset($setting['category'], $setting['key'], $setting['value'])) {
                    $sql = "INSERT INTO settings (category, key, value, type, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))";
                    
                    try {
                        Database::execute($sql, [
                            $setting['category'],
                            $setting['key'],
                            $setting['value'],
                            $setting['type'] ?? 'string',
                            $setting['description'] ?? ''
                        ]);
                        $imported++;
                        echo "✅ Восстановлена: {$setting['category']}.{$setting['key']}\n";
                    } catch (Exception $e) {
                        echo "❌ Ошибка восстановления {$setting['category']}.{$setting['key']}: " . $e->getMessage() . "\n";
                    }
                }
            }
            echo "✅ Восстановлено настроек: $imported\n";
        } else {
            echo "❌ Ошибка чтения JSON настроек\n";
        }
    } else {
        echo "❌ Резервная копия настроек не найдена\n";
    }
} else {
    echo "❌ Резервные копии не найдены\n";
}

// 2. Восстанавливаем пользователей из референса
echo "\n👥 ВОССТАНОВЛЕНИЕ ПОЛЬЗОВАТЕЛЕЙ:\n";
$referenceFile = 'REFERENCE/api/user_data.json';

if (file_exists($referenceFile)) {
    echo "Найден файл пользователей: $referenceFile\n";
    
    $jsonData = file_get_contents($referenceFile);
    $userData = json_decode($jsonData, true);
    
    if ($userData && is_array($userData)) {
        echo "Найдено пользователей в референсе: " . count($userData) . "\n";
        
        $imported = 0;
        $errors = 0;
        
        foreach ($userData as $telegramId => $userInfo) {
            try {
                $sql = "INSERT INTO users (
                    telegram_id, first_name, last_name, username, language, balance, 
                    total_earned, referred_by, referrals_count, referral_earnings, 
                    withdrawals_count, suspicious_activity, blocked, 
                    created_at, updated_at, last_activity
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $params = [
                    (int)$telegramId,
                    $userInfo['first_name'] ?? '',
                    $userInfo['last_name'] ?? '',
                    $userInfo['username'] ?? null,
                    $userInfo['language'] ?? 'ru',
                    $userInfo['balance'] ?? 0,
                    $userInfo['total_earned'] ?? 0,
                    $userInfo['referrer_id'] ?? null,
                    $userInfo['referrals_count'] ?? 0,
                    $userInfo['referral_earnings'] ?? 0,
                    $userInfo['withdrawals_count'] ?? 0,
                    $userInfo['suspicious_activity'] ?? 0,
                    $userInfo['blocked'] ?? false ? 1 : 0,
                    isset($userInfo['registered_at']) ? date('Y-m-d H:i:s', $userInfo['registered_at']) : date('Y-m-d H:i:s'),
                    date('Y-m-d H:i:s'),
                    isset($userInfo['last_activity']) ? date('Y-m-d H:i:s', $userInfo['last_activity']) : date('Y-m-d H:i:s')
                ];
                
                if (Database::execute($sql, $params) > 0) {
                    $imported++;
                    if ($imported % 100 === 0) {
                        echo "Импортировано: $imported\n";
                    }
                } else {
                    $errors++;
                }
                
            } catch (Exception $e) {
                $errors++;
                if ($errors <= 10) { // Показываем только первые 10 ошибок
                    echo "❌ Ошибка импорта $telegramId: " . $e->getMessage() . "\n";
                }
            }
        }
        
        echo "✅ Импортировано пользователей: $imported\n";
        echo "❌ Ошибок: $errors\n";
    } else {
        echo "❌ Ошибка чтения JSON пользователей\n";
    }
} else {
    echo "❌ Файл пользователей не найден: $referenceFile\n";
}

// 3. Проверяем результат
echo "\n📊 ПРОВЕРКА РЕЗУЛЬТАТА:\n";
try {
    $settingsCount = Database::query("SELECT COUNT(*) as cnt FROM settings")[0]['cnt'];
    echo "Настроек в базе: $settingsCount\n";
    
    $usersCount = Database::query("SELECT COUNT(*) as cnt FROM users")[0]['cnt'];
    echo "Пользователей в базе: $usersCount\n";
    
    $adminsCount = Database::query("SELECT COUNT(*) as cnt FROM admin_users")[0]['cnt'];
    echo "Админов в базе: $adminsCount\n";
    
} catch (Exception $e) {
    echo "❌ Ошибка проверки: " . $e->getMessage() . "\n";
}

// 4. Исправляем проблему с COUNT в моделях
echo "\n🔧 ИСПРАВЛЕНИЕ МОДЕЛЕЙ:\n";
echo "Нужно исправить модели, чтобы использовать 'cnt' вместо 'count' в SQLite\n";

echo "\n🎯 РЕЗУЛЬТАТ:\n";
echo "✅ Данные восстановлены\n";
echo "✅ База данных заполнена\n";
echo "⚠️  Нужно исправить модели для работы с SQLite\n";

echo "\n=== КОНЕЦ ВОССТАНОВЛЕНИЯ ===\n";
?>
