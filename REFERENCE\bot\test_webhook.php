<?php
/**
 * test_webhook.php
 * Тестовый файл для проверки работы webhook
 */

require_once __DIR__ . '/config.php';

// Тестируем отправку сообщения
echo "Тестирование Telegram бота...\n";

// Проверяем, что токен установлен
if (!defined('BOT_TOKEN') || empty(BOT_TOKEN)) {
    echo "ОШИБКА: Токен бота не установлен!\n";
    exit;
}

echo "Токен бота: " . substr(BOT_TOKEN, 0, 10) . "...\n";
echo "URL webhook: " . WEBHOOK_URL . "\n";
echo "URL приложения: " . WEBAPP_URL . "\n";

// Проверяем доступность Telegram API
$response = telegramRequest('getMe');
if ($response) {
    echo "✅ Бот успешно подключен к Telegram API\n";
    echo "Имя бота: " . $response['first_name'] . "\n";
    echo "Username: @" . $response['username'] . "\n";
} else {
    echo "❌ Ошибка подключения к Telegram API\n";
}

// Проверяем webhook
echo "\nПроверка webhook...\n";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo) {
    echo "URL webhook: " . ($webhookInfo['url'] ?: 'не установлен') . "\n";
    echo "Последняя ошибка: " . ($webhookInfo['last_error_message'] ?: 'нет') . "\n";
    echo "Количество ожидающих обновлений: " . ($webhookInfo['pending_update_count'] ?: 0) . "\n";
} else {
    echo "❌ Ошибка получения информации о webhook\n";
}

echo "\nТест завершен.\n";
?>
