# 🤖 Инструкция по настройке Webhook для Telegram бота

## 📋 Предварительные требования

1. **Сервер с PHP** (версия 7.4+)
2. **SSL сертификат** (HTTPS обязателен для webhook)
3. **Доступ к серверу** через SSH или панель управления
4. **Токен бота** от @BotFather

## 🚀 Способы активации webhook

### Способ 1: Автоматический (рекомендуется)

1. **Загрузите файлы на сервер:**
   ```
   /bot/config.php
   /bot/webhook.php
   /bot/setup_webhook.php
   /api/db_mock.php
   /includes/Localization.php
   /locales/ru.json
   /locales/en.json
   ```

2. **Проверьте настройки в config.php:**
   ```php
   define('BOT_TOKEN', '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA');
   define('WEBHOOK_URL', 'https://app.uniqpaid.com/test3/bot/webhook.php');
   define('WEBAPP_URL', 'https://app.uniqpaid.com/test3/');
   ```

3. **Запустите скрипт установки:**
   ```bash
   php bot/setup_webhook.php
   ```

### Способ 2: Через cURL

```bash
curl -X POST "https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/setWebhook" \
     -d "url=https://app.uniqpaid.com/test3/bot/webhook.php" \
     -d "allowed_updates=[\"message\",\"callback_query\"]"
```

### Способ 3: Через браузер

Откройте в браузере:
```
https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/setWebhook?url=https://app.uniqpaid.com/test3/bot/webhook.php
```

## 🔍 Проверка статуса

### Автоматическая проверка:
```bash
php bot/check_webhook.php
```

### Ручная проверка:
```
https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/getWebhookInfo
```

## 🧪 Тестирование

1. **Откройте бота:** https://t.me/uniqpaid_paid_bot
2. **Отправьте команду:** `/start`
3. **Проверьте кнопки:** Баланс, Друзья, Статистика
4. **Запустите приложение:** Кнопка "🚀 Запустить приложение"

## 🛠️ Устранение проблем

### Webhook не активируется:
- Проверьте SSL сертификат
- Убедитесь, что файл webhook.php доступен по HTTPS
- Проверьте права доступа к файлам (644 для файлов, 755 для папок)

### Бот не отвечает:
- Проверьте логи: `tail -f bot/bot.log`
- Убедитесь, что все файлы загружены
- Проверьте синтаксис PHP: `php -l bot/webhook.php`

### Ошибки в логах:
- Проверьте права записи для файла `api/user_data.json`
- Убедитесь, что папка `bot/` доступна для записи (для логов)

## 📁 Структура файлов на сервере

```
/
├── bot/
│   ├── config.php          # Конфигурация бота
│   ├── webhook.php         # Основной файл webhook
│   ├── setup_webhook.php   # Скрипт установки
│   ├── check_webhook.php   # Скрипт проверки
│   └── bot.log            # Логи бота
├── api/
│   ├── db_mock.php        # База данных
│   └── user_data.json     # Данные пользователей
├── includes/
│   └── Localization.php   # Система локализации
├── locales/
│   ├── ru.json           # Русские переводы
│   └── en.json           # Английские переводы
└── index.html            # Мини-приложение
```

## 🔧 Полезные команды

### Удаление webhook:
```bash
php bot/remove_webhook.php
```

### Просмотр логов:
```bash
tail -f bot/bot.log
```

### Проверка синтаксиса:
```bash
php -l bot/webhook.php
```

## 📞 Поддержка

Если возникли проблемы:
1. Проверьте логи бота
2. Убедитесь, что все файлы загружены
3. Проверьте настройки в config.php
4. Протестируйте доступность webhook URL

## ✅ Готово!

После успешной настройки webhook бот будет:
- Отвечать на команды `/start`, `/balance`, `/stats`, `/help`
- Поддерживать русский и английский языки
- Работать с inline кнопками
- Интегрироваться с мини-приложением
- Вести логи всех операций
