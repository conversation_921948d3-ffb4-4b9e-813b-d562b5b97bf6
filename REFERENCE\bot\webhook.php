<?php
/**
 * webhook.php
 * Основной файл обработки webhook от Telegram
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../api/db_mock.php';

/**
 * Загрузка текстов бота из файла
 */
function loadBotTextsFromFile() {
    static $texts = null;

    if ($texts === null) {
        // Пробуем разные пути к файлу переводов
        $possiblePaths = [
            __DIR__ . '/bot_texts.json',           // Если вызывается из bot/
            dirname(__DIR__) . '/bot/bot_texts.json', // Если вызывается из корня
            'bot/bot_texts.json'                   // Относительный путь
        ];

        $textsFile = null;
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                $textsFile = $path;
                break;
            }
        }

        if ($textsFile) {
            botLog("DEBUG: Загружаем переводы из: {$textsFile}");
            $content = file_get_contents($textsFile);
            $texts = json_decode($content, true);

            if ($texts) {
                botLog("DEBUG: Переводы загружены успешно, языки: " . implode(', ', array_keys($texts)));
            } else {
                botLog("ERROR: Ошибка декодирования JSON из файла: {$textsFile}");
                $texts = false;
            }
        } else {
            botLog("ERROR: Файл переводов не найден ни по одному пути: " . implode(', ', $possiblePaths));
            $texts = false;
        }
    }

    return $texts;
}

/**
 * Получение текста бота из файла
 */
function getBotTextFromFile($key, $lang = 'ru', $params = []) {
    $texts = loadBotTextsFromFile();

    botLog("DEBUG: Запрос текста '{$key}' для языка '{$lang}'");

    // Сначала пробуем запрошенный язык
    if ($texts && isset($texts[$lang])) {
        $keys = explode('.', $key);
        $value = $texts[$lang];

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                botLog("DEBUG: Ключ '{$k}' не найден в структуре текстов для пути '{$key}'");
                $value = null;
                break;
            }
            $value = $value[$k];
        }

        if ($value !== null) {
            // Заменяем параметры в тексте
            if (!empty($params)) {
                foreach ($params as $param => $replacement) {
                    $value = str_replace('{' . $param . '}', $replacement, $value);
                }
            }
            botLog("DEBUG: Найден текст для ключа '{$key}': " . substr($value, 0, 30) . (strlen($value) > 30 ? '...' : ''));
            return $value;
        }
    }

    // Fallback на английский, если запрошенный язык не английский
    if ($lang !== 'en' && $texts && isset($texts['en'])) {
        botLog("DEBUG: Текст для ключа '{$key}' на языке '{$lang}' не найден. Пробуем английский fallback.");
        $keys = explode('.', $key);
        $value = $texts['en'];

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                botLog("DEBUG: Ключ '{$k}' не найден в английской структуре текстов");
                return null;
            }
            $value = $value[$k];
        }

        // Заменяем параметры в тексте
        if (!empty($params)) {
            foreach ($params as $param => $replacement) {
                $value = str_replace('{' . $param . '}', $replacement, $value);
            }
        }
        return $value;
    }

    botLog("DEBUG: Текст для ключа '{$key}' не найден ни на одном языке");
    return null;
}

// Пробуем подключить локализацию, но не падаем если её нет

/**
 * Безопасное получение перевода
 */
function getTranslation($key, $params = [], $fallback = null, $userLanguage = null) {
    // Если язык не передан, определяем его
    if ($userLanguage === null) {
        $userLanguage = getCurrentUserLanguage();
    }

    // Сначала пробуем получить текст для текущего языка
    $text = getBotTextFromFile($key, $userLanguage, $params);
    if ($text !== null) {
        return $text;
    }

    // Если для текущего языка не нашли, пробуем английский
    $text = getBotTextFromFile($key, 'en', $params);
    if ($text !== null) {
        botLog("WARNING: Текст для ключа '{$key}' на языке '{$userLanguage}' не найден. Использован английский fallback.");
        return $text;
    }

    // Если не нашли, используем fallback
    if ($fallback !== null) {
        foreach ($params as $param => $value) {
            $fallback = str_replace('{' . $param . '}', $value, $fallback);
        }
        return $fallback;
    }

    return $key;
}

/**
 * Получение вложенного значения из массива по ключу с точками
 */
function getNestedValue($array, $key) {
    $keys = explode('.', $key);
    $value = $array;

    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return null;
        }
    }

    return is_string($value) ? $value : null;
}

// Получаем данные от Telegram
$input = file_get_contents('php://input');
$update = json_decode($input, true);

if (!$update) {
    botLog("ERROR: Не удалось декодировать JSON от Telegram");
    exit;
}

botLog("INFO: Получен update: " . json_encode($update));

// Устанавливаем глобальные переменные для функций определения языка
$message = null;
$callbackQuery = null;

// Обрабатываем сообщение
if (isset($update['message'])) {
    $message = $update['message']; // Устанавливаем глобальную переменную
    handleMessage($update['message']);
}

// Обрабатываем callback query (нажатие на inline кнопки)
if (isset($update['callback_query'])) {
    $callbackQuery = $update['callback_query']; // Устанавливаем глобальную переменную
    handleCallbackQuery($update['callback_query']);
}

/**
 * Обработка текстовых сообщений
 */
function handleMessage($message) {
    $chatId = $message['chat']['id'];
    $userId = $message['from']['id'];
    $text = $message['text'] ?? '';
    // Определяем язык пользователя для fallback имени
    $userLang = getUserLanguage($userId, $message['from']);
    $firstName = $message['from']['first_name'] ?? getBotTextFromFile('system.user_fallback', $userLang) ?: ($userLang === 'ru' ? 'Пользователь' : 'User');
    $lastName = $message['from']['last_name'] ?? '';
    $username = $message['from']['username'] ?? '';

    botLog("INFO: Сообщение от пользователя {$userId} ({$firstName}): {$text}");

    // Команда /start
    if (strpos($text, '/start') === 0) {
        handleStartCommand($chatId, $userId, $firstName, $lastName, $username, $text, $message);
        return;
    }

    // Команда /help
    if ($text === '/help') {
        handleHelpCommand($chatId, $userId);
        return;
    }

    // Команда /balance
    if ($text === '/balance') {
        handleBalanceCommand($chatId, $userId);
        return;
    }

    // Команда /stats
    if ($text === '/stats') {
        handleStatsCommand($chatId, $userId);
        return;
    }

    // По умолчанию показываем главное меню
    showMainMenu($chatId, $firstName, $userId);
}

/**
 * Обработка команды /start
 */
function handleStartCommand($chatId, $userId, $firstName, $lastName, $username, $text, $message) {
    // Проверяем, есть ли реферальный код
    $referrerId = null;
    if (preg_match('/\/start\s+(\d+)/', $text, $matches)) {
        $referrerId = (int)$matches[1];
        botLog("INFO: Пользователь {$userId} пришел по реферальной ссылке от {$referrerId}");
    }

    // Определяем язык пользователя на основе Telegram данных
    $userLanguage = getUserLanguage($userId, $message['from']);

    // ОТЛАДКА: Логируем определение языка
    botLog("DEBUG: handleStartCommand - userId: {$userId}, language_code: " . ($message['from']['language_code'] ?? 'НЕТ') . ", определенный язык: {$userLanguage}");

    // Регистрируем или обновляем пользователя с передачей Telegram данных
    registerUser($userId, $firstName, $lastName, $username, $referrerId, $userLanguage, $message['from']);

    // Пробуем построить приветствие из новой структуры
    $title = getBotTextFromFile('welcome.title', $userLanguage);
    $subtitle = getBotTextFromFile('welcome.subtitle', $userLanguage);
    $description = getBotTextFromFile('welcome.description', $userLanguage);
    $adInfo = getBotTextFromFile('welcome.ad_info', $userLanguage);
    $warning = getBotTextFromFile('welcome.warning', $userLanguage);
    $howWorksTitle = getBotTextFromFile('welcome.how_works_title', $userLanguage);
    $earnCoins = getBotTextFromFile('welcome.earn_coins', $userLanguage);
    $inviteFriends = getBotTextFromFile('welcome.invite_friends', $userLanguage);
    $withdrawCrypto = getBotTextFromFile('welcome.withdraw_crypto', $userLanguage);
    $exchangeRate = getBotTextFromFile('welcome.exchange_rate', $userLanguage);
    $startEarning = getBotTextFromFile('welcome.start_earning', $userLanguage);

    // ОТЛАДКА: Логируем получение текстов
    botLog("DEBUG: handleStartCommand - получение текстов для языка: {$userLanguage}");
    botLog("DEBUG: welcome.title = " . ($title ?: "НЕТ"));

    if ($title) {
        // Строим текст из новой структуры
        botLog("DEBUG: Используем тексты из новой структуры");
        $welcomeText = $title . "\n\n";
        if ($subtitle) $welcomeText .= "<b>" . $subtitle . "</b>\n";
        if ($description) $welcomeText .= "<b>" . $description . "</b>\n\n";
        if ($adInfo) $welcomeText .= $adInfo . "\n\n";
        if ($warning) $welcomeText .= $warning . "\n\n";
        if ($howWorksTitle) $welcomeText .= "<b>" . $howWorksTitle . "</b>\n";
        $welcomeText .= "💰 10 монет за каждый просмотр\n";
        $welcomeText .= "🔄 Вывод в BTC, USDT, TON, ETH\n";
        $welcomeText .= "👥 Реферальная программа 10%\n\n";
        if ($exchangeRate) $welcomeText .= $exchangeRate . "\n\n";
        if ($startEarning) $welcomeText .= "<b>" . $startEarning . "</b>";
    } else {
        // Fallback: создаем приветственное сообщение с правильными многоязычными fallback
        botLog("DEBUG: Используем fallback тексты для языка: {$userLanguage}");
        $welcomeTitle = getBotTextFromFile('welcome.title', $userLanguage) ?:
                       ($userLanguage === 'ru' ? "🎉 Добро пожаловать в UniQPaid!" : "🎉 Welcome to UniQPaid!");
        $welcomeSubtitle = getBotTextFromFile('welcome.subtitle', $userLanguage) ?:
                          ($userLanguage === 'ru' ? "💰 Просто смотри рекламу сейчас и получай крипту сразу!" : "💰 Just watch ads now and get crypto instantly!");
        $welcomeDescription = getBotTextFromFile('welcome.description', $userLanguage) ?:
                             ($userLanguage === 'ru' ? "🚀 Не нужно ждать листинга! Моментальные автовыплаты на кошелёк!" : "🚀 No need to wait for listing! Instant auto-payouts to wallet!");
        $adInfo = getBotTextFromFile('welcome.ad_info', $userLanguage) ?:
                 ($userLanguage === 'ru' ? "📊 Регулярно проверяйте приложение на наличие новых рекламных предложений. Каждый день доступно 20 переходов по ссылкам, 20 баннеров и 20 видео." : "📊 Regularly check the app for new advertising offers. 20 link clicks, 20 banners and 20 videos are available daily.");
        $withdrawalWarning = getBotTextFromFile('welcome.warning', $userLanguage) ?:
                            ($userLanguage === 'ru' ? "⚠️ Будьте внимательны к размеру выводимой суммы. Слишком малые заказы могут зависнуть в блокчейне из-за минимальных лимитов бирж или кошельков." : "⚠️ Be careful with withdrawal amount size. Too small orders may get stuck in blockchain due to minimum limits of exchanges or wallets.");
        
        $howWorksTitle = $userLanguage === 'ru' ? "<b>Получайте криптовалюту:</b>" : "<b>Get cryptocurrency:</b>";
        if ($userLanguage === 'ru') {
            $howWorksItems = "💰 10 монет за каждый просмотр\n" .
                             "🔄 Вывод в BTC, USDT, TON, ETH\n" .
                             "👥 Реферальная программа 10%";
        } else {
            $howWorksItems = "💰 10 coins for each view\n" .
                             "🔄 Withdraw in BTC, USDT, TON, ETH\n" .
                             "👥 10% Referral Program";
        }
        
        $exchangeRate = getBotTextFromFile('welcome.exchange_rate', $userLanguage) ?:
                       ($userLanguage === 'ru' ? "💎 Курс: 10 монет = $0.01 USD" : "💎 Rate: 10 coins = $0.01 USD");
        $startEarning = getBotTextFromFile('welcome.start_earning', $userLanguage) ?:
                       ($userLanguage === 'ru' ? "🚀 Начните зарабатывать прямо сейчас!" : "🚀 Start earning right now!");

        $welcomeText = $welcomeTitle . "\n\n";
        $welcomeText .= "<b>" . $welcomeSubtitle . "</b>\n";
        $welcomeText .= "<b>" . $welcomeDescription . "</b>\n\n";
        $welcomeText .= $adInfo . "\n\n";
        $welcomeText .= $withdrawalWarning . "\n\n";
        $welcomeText .= $howWorksTitle . "\n";
        $welcomeText .= $howWorksItems . "\n\n";
        $welcomeText .= $exchangeRate . "\n\n";
        $welcomeText .= "<b>" . $startEarning . "</b>";
    }

    // Создаем динамическую клавиатуру с учетом настроек видимости
    $keyboardRows = [];

    // Кнопка запуска приложения (всегда видна)
    if (isButtonVisible('launch_app')) {
        $keyboardRows[] = [
            [
                'text' => getBotTextFromFile('buttons.launch_app', $userLanguage) ?:
                          ($userLanguage === 'ru' ? "🚀 Запустить приложение" : "🚀 Launch App"),
                'web_app' => ['url' => WEBAPP_URL]
            ]
        ];
    }


    // Третья строка: Баланс и Друзья
    $thirdRow = [];
    if (isButtonVisible('my_balance')) {
        $thirdRow[] = [
            'text' => getBotTextFromFile('buttons.my_balance', $userLanguage) ?:
                      ($userLanguage === 'ru' ? "💰 Мой баланс" : "💰 My Balance"),
            'callback_data' => 'my_balance'
        ];
    }
    if (isButtonVisible('friends')) {
        $thirdRow[] = [
            'text' => getBotTextFromFile('buttons.friends', $userLanguage) ?:
                      ($userLanguage === 'ru' ? "👥 Друзья" : "👥 Friends"),
            'callback_data' => 'invite_friends'
        ];
    }
    if (!empty($thirdRow)) {
        $keyboardRows[] = $thirdRow;
    }

    // Четвертая строка: Статистика и Помощь
    $fourthRow = [];
    if (isButtonVisible('statistics')) {
        $fourthRow[] = [
            'text' => getBotTextFromFile('buttons.statistics', $userLanguage) ?:
                      ($userLanguage === 'ru' ? "📊 Статистика" : "📊 Statistics"),
            'callback_data' => 'my_stats'
        ];
    }
    if (isButtonVisible('help')) {
        $fourthRow[] = [
            'text' => getBotTextFromFile('buttons.help', $userLanguage) ?:
                      ($userLanguage === 'ru' ? "❓ Помощь" : "❓ Help"),
            'callback_data' => 'help'
        ];
    }
    if (!empty($fourthRow)) {
        $keyboardRows[] = $fourthRow;
    }

    $keyboard = ['inline_keyboard' => $keyboardRows];

    // 🌈 СУПЕР БАННЕР для приветственного сообщения
    // ВАЖНО: Telegram Bot API НЕ поддерживает SVG! Используем PNG версию
    // Добавляем timestamp для обхода кэша Telegram
    $timestamp = time();
    $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?v=' . $timestamp;

    // Пробуем отправить с логотипом
    $result = sendPhoto($chatId, $logoUrl, $welcomeText, $keyboard);

    if (!$result) {
        // Если логотип не отправился, отправляем красивое текстовое сообщение
        botLog("WARNING: Не удалось отправить картинку, отправляем текст с эмодзи");

        // Добавляем красивый заголовок с эмодзи
        $welcomeHeader = getBotTextFromFile('system.welcome_header', $userLanguage) ?:
                        ($userLanguage === 'ru' ? "🎨🚀💎 <b>UniQPaid - Криптозаработок</b> 💎🚀🎨" : "🎨🚀💎 <b>UniQPaid - Crypto Earnings</b> 💎🚀🎨") . "\n\n";
        $fullWelcomeText = $welcomeHeader . $welcomeText;

        sendMessage($chatId, $fullWelcomeText, $keyboard);
    } else {
        botLog("INFO: Картинка успешно отправлена пользователю {$userId}");
    }
}

/**
 * Регистрация или обновление пользователя
 */
function registerUser($userId, $firstName, $lastName, $username, $referrerId = null, $userLanguage = 'ru', $telegramUser = null) {
    $userData = loadUserData();
    $isNewUser = !isset($userData[$userId]);

    if ($isNewUser) {
        // Для нового пользователя определяем язык автоматически
        if ($telegramUser) {
            $userLanguage = detectUserLanguage($telegramUser);
        }

        // Новый пользователь
        $userData[$userId] = [
            'balance' => 0,
            'total_earned' => 0,
            'withdrawals' => [],
            'withdrawal_log' => [],
            'referrer_id' => $referrerId,
            'referrals' => [],
            'referral_earnings' => 0,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'username' => $username,
            'language' => $userLanguage,
            'registered_at' => time(),
            'last_activity' => time(),
            'suspicious_activity_count' => 0,
            'withdrawals_count' => 0
        ];

        botLog("INFO: Зарегистрирован новый пользователь {$userId} ({$firstName}) с языком {$userLanguage}");

        // Если есть реферер, добавляем к нему реферала
        if ($referrerId && isset($userData[$referrerId])) {
            if (!isset($userData[$referrerId]['referrals'])) {
                $userData[$referrerId]['referrals'] = [];
            }

            $userData[$referrerId]['referrals'][] = [
                'user_id' => $userId,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'username' => $username,
                'registered_at' => time()
            ];

            botLog("INFO: Пользователь {$userId} добавлен как реферал к {$referrerId}");
        }
    } else {
        // Обновляем данные существующего пользователя
        $userData[$userId]['first_name'] = $firstName;
        $userData[$userId]['last_name'] = $lastName;
        $userData[$userId]['username'] = $username;
        $userData[$userId]['last_activity'] = time();

        // Обновляем язык если он изменился в Telegram
        if ($telegramUser) {
            $newLanguage = detectUserLanguage($telegramUser);
            if ($userData[$userId]['language'] !== $newLanguage) {
                $userData[$userId]['language'] = $newLanguage;
                botLog("INFO: Обновлен язык пользователя {$userId} на {$newLanguage}");
            }
        }
    }

    saveUserData($userData);
    return $isNewUser;
}

/**
 * Определение языка пользователя на основе Telegram данных
 */
function detectUserLanguage($telegramUser) {
    // Получаем язык из Telegram
    $telegramLanguage = $telegramUser['language_code'] ?? 'en';
    $baseLanguage = substr($telegramLanguage, 0, 2); // Берем только основу языка

    botLog("INFO: Определение языка - language_code из Telegram: " . $telegramLanguage . ", основа: " . $baseLanguage);

    // Список русскоязычных языков
    $russianLanguages = ['ru', 'be', 'uk', 'kk', 'ky', 'uz', 'tg', 'az', 'hy', 'ka', 'ro'];

    // Для английского и его вариантов возвращаем 'en'
    $englishLanguages = ['en', 'us', 'gb', 'au', 'ca', 'nz', 'ie', 'za', 'jm', 'bz', 'tt', 'ng'];
    
    if (in_array($baseLanguage, $russianLanguages)) {
        botLog("INFO: Язык {$baseLanguage} определен как русскоязычный");
        return 'ru';
    } elseif (in_array($baseLanguage, $englishLanguages)) {
        botLog("INFO: Язык {$baseLanguage} определен как английский");
        return 'en';
    }

    // Для всех остальных языков также используем английский
    botLog("INFO: Язык {$baseLanguage} не распознан, используем английский");
    return 'en';
}

/**
 * Получение языка пользователя - ВСЕГДА из текущих настроек Telegram
 */
function getUserLanguage($userId, $telegramUser = null) {
    // ОТЛАДКА: Логируем входные параметры
    $telegramLangCode = $telegramUser['language_code'] ?? 'НЕТ';
    botLog("DEBUG: getUserLanguage - userId: {$userId}, telegramUser: " . ($telegramUser ? "ЕСТЬ (language_code: {$telegramLangCode})" : "НЕТ"));

    // ВСЕГДА используем данные из Telegram, если они есть (приоритет текущих настроек)
    if ($telegramUser && isset($telegramUser['language_code'])) {
        $detectedLanguage = detectUserLanguage($telegramUser);
        botLog("INFO: Используем ТЕКУЩИЙ язык из Telegram для пользователя {$userId}: {$detectedLanguage}");
        return $detectedLanguage;
    }

    // Если нет данных Telegram, используем английский по умолчанию
    // (международный стандарт)
    botLog("INFO: Нет данных Telegram для пользователя {$userId}, используем английский по умолчанию");
    return 'en';
}

/**
 * Получение языка из глобального сообщения (для использования в обработчиках команд)
 */
function getCurrentUserLanguage($userId = null) {
    global $message, $callbackQuery;

    // Пробуем получить данные Telegram из текущего сообщения или callback
    $telegramUser = null;
    if (isset($message['from'])) {
        $telegramUser = $message['from'];
        // Если userId не передан, берем из сообщения
        if ($userId === null) {
            $userId = $message['from']['id'];
        }
    } elseif (isset($callbackQuery['from'])) {
        $telegramUser = $callbackQuery['from'];
        // Если userId не передан, берем из callback
        if ($userId === null) {
            $userId = $callbackQuery['from']['id'];
        }
    }

    // Если всё ещё нет userId, возвращаем английский по умолчанию
    if ($userId === null) {
        return 'en';
    }

    return getUserLanguage($userId, $telegramUser);
}

/**
 * Получение имени пользователя
 */
function getUserFirstName($userId) {
    $userData = loadUserData();

    // Определяем язык пользователя для fallback
    $userLang = getCurrentUserLanguage($userId);

    if (isset($userData[$userId])) {
        return $userData[$userId]['first_name'] ?? getBotTextFromFile('system.user_fallback', $userLang) ?: ($userLang === 'ru' ? 'Пользователь' : 'User');
    }

    return getBotTextFromFile('system.user_fallback', $userLang) ?: ($userLang === 'ru' ? 'Пользователь' : 'User');
}

// === ФУНКЦИИ ДЛЯ РАБОТЫ С НАСТРОЙКАМИ ВИДИМОСТИ КНОПОК ===

/**
 * Загрузка настроек видимости кнопок
 */
function loadButtonVisibilitySettings() {
    static $settings = null;

    if ($settings === null) {
        $visibilityFile = __DIR__ . '/button_visibility.json';

        if (file_exists($visibilityFile)) {
            $content = file_get_contents($visibilityFile);
            $settings = json_decode($content, true);

            if ($settings) {
                botLog("DEBUG: Настройки видимости кнопок загружены успешно");
            } else {
                botLog("ERROR: Ошибка декодирования настроек видимости");
                $settings = getDefaultButtonVisibility();
            }
        } else {
            botLog("WARNING: Файл настроек видимости не найден, используем настройки по умолчанию");
            $settings = getDefaultButtonVisibility();
        }
    }

    return $settings;
}

/**
 * Получение настроек видимости по умолчанию
 */
function getDefaultButtonVisibility() {
    return [
        'launch_app' => true,
        'detailed_info' => true,
        'how_it_works' => true,
        'my_balance' => true,
        'friends' => true,
        'statistics' => true,
        'help' => true
    ];
}

/**
 * Проверка видимости кнопки
 */
function isButtonVisible($buttonKey) {
    $settings = loadButtonVisibilitySettings();
    return isset($settings[$buttonKey]) ? $settings[$buttonKey] : true;
}

/**
 * Показ главного меню
 */
function showMainMenu($chatId, $firstName, $userId = null) {
    // Получаем язык пользователя
    $userLanguage = getCurrentUserLanguage($userId);

    // Пробуем получить тексты из новой структуры
    $hello = getBotTextFromFile('main_menu.hello', $userLanguage, ['name' => $firstName]);
    $chooseAction = getBotTextFromFile('main_menu.choose_action', $userLanguage);

    if ($hello && $chooseAction) {
        $text = $hello . "\n\n" . $chooseAction;
    } else {
        // Fallback с правильными многоязычными текстами
        $helloText = getBotTextFromFile('main_menu.hello', $userLanguage) ?:
                    ($userLanguage === 'ru' ? "👋 Привет, {$firstName}!" : "👋 Hello, {$firstName}!");
        $chooseActionText = getBotTextFromFile('main_menu.choose_action', $userLanguage) ?:
                           ($userLanguage === 'ru' ? "Выберите действие:" : "Choose an action:");

        $text = $helloText . "\n\n" . $chooseActionText;
    }

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?:
                              ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App'),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.balance', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "💰 Баланс" : "💰 Balance"),
                    'callback_data' => 'my_balance'
                ],
                [
                    'text' => getBotTextFromFile('buttons.friends', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "👥 Друзья" : "👥 Friends"),
                    'callback_data' => 'invite_friends'
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.statistics', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "📊 Статистика" : "📊 Statistics"),
                    'callback_data' => 'my_stats'
                ],
                [
                    'text' => getBotTextFromFile('buttons.help', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "❓ Помощь" : "❓ Help"),
                    'callback_data' => 'help'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка callback query (нажатие на inline кнопки)
 */
function handleCallbackQuery($callbackQuery) {
    $chatId = $callbackQuery['message']['chat']['id'];
    $userId = $callbackQuery['from']['id'];
    $data = $callbackQuery['data'];
    $messageId = $callbackQuery['message']['message_id'];

    botLog("INFO: Callback query от {$userId}: {$data}");

    // Подтверждаем получение callback
    telegramRequest('answerCallbackQuery', ['callback_query_id' => $callbackQuery['id']]);

    switch ($data) {
        case 'my_balance':
            handleBalanceCommand($chatId, $userId);
            break;

        case 'invite_friends':
            handleInviteFriends($chatId, $userId);
            break;

        case 'my_stats':
            handleStatsCommand($chatId, $userId);
            break;

        case 'help':
            handleHelpCommand($chatId, $userId);
            break;

        case 'detailed_info':
            handleDetailedInfo($chatId, $userId);
            break;

        case 'how_it_works':
            handleHowItWorks($chatId, $userId);
            break;

        case 'main_menu':
            $firstName = getUserFirstName($userId);
            showMainMenu($chatId, $firstName, $userId);
            break;

        default:
            $unknownCommand = getBotTextFromFile('system.unknown_command', getCurrentUserLanguage($userId)) ?:
                             (getCurrentUserLanguage($userId) === 'ru' ? "Неизвестная команда." : "Unknown command.");
            sendMessage($chatId, $unknownCommand);
    }
}

/**
 * Обработка команды баланса
 */
function handleBalanceCommand($chatId, $userId) {
    $userData = loadUserData();
    $userLanguage = getCurrentUserLanguage($userId);

    if (!isset($userData[$userId])) {
        $errorText = getBotTextFromFile('common.user_not_found', $userLanguage) ?:
                    getTranslation('bot.user_not_found', [], "❌ Пользователь не найден. Отправьте /start для регистрации.", $userLanguage);
        sendMessage($chatId, $errorText);
        return;
    }

    $user = $userData[$userId];
    $balance = $user['balance'] ?? 0;
    $totalEarned = $user['total_earned'] ?? 0;
    $referralEarnings = $user['referral_earnings'] ?? 0;
    $withdrawalsCount = $user['withdrawals_count'] ?? 0;

    // Правильно форматируем доллары
    $usdAmount = $balance * COIN_VALUE_USD;
    $formattedUsd = $usdAmount == floor($usdAmount) ? number_format($usdAmount, 0) : rtrim(rtrim(number_format($usdAmount, 3), '0'), '.');

    // Пробуем построить текст баланса из новой структуры
    $title = getBotTextFromFile('balance.title', $userLanguage);
    $currentBalance = getBotTextFromFile('balance.current_balance', $userLanguage, ['balance' => $balance]);
    $balanceUsd = getBotTextFromFile('balance.balance_usd', $userLanguage, ['amount' => $formattedUsd]);
    $totalEarnedText = getBotTextFromFile('balance.total_earned', $userLanguage, ['amount' => $totalEarned]);
    $fromReferrals = getBotTextFromFile('balance.from_referrals', $userLanguage, ['amount' => $referralEarnings]);
    $withdrawalsCountText = getBotTextFromFile('balance.withdrawals_count', $userLanguage, ['count' => $withdrawalsCount]);
    $openAppWithdraw = getBotTextFromFile('balance.open_app_withdraw', $userLanguage);

    if ($title) {
        // Строим текст из новой структуры
        $text = "<b>" . $title . "</b>\n\n";
        if ($currentBalance) $text .= $currentBalance . "\n";
        if ($balanceUsd) $text .= $balanceUsd . "\n\n";
        if ($totalEarnedText) $text .= $totalEarnedText . "\n";
        if ($fromReferrals) $text .= $fromReferrals . "\n";
        if ($withdrawalsCountText) $text .= $withdrawalsCountText . "\n\n";
    } else {
        // Fallback с правильными многоязычными текстами
        $yourBalance = $userLanguage === 'ru' ? "💰 Ваш баланс" : "💰 Your Balance";
        $currentBalanceText = $userLanguage === 'ru' ? "🪙 Текущий баланс: {$balance} монет" : "🪙 Current balance: {$balance} coins";
        $balanceUsdText = $userLanguage === 'ru' ? "💵 В долларах: $" . $formattedUsd . " USD" : "💵 In USD: $" . $formattedUsd . " USD";
        $totalEarnedText = $userLanguage === 'ru' ? "📈 Всего заработано: {$totalEarned} монет" : "📈 Total earned: {$totalEarned} coins";
        $fromReferralsText = $userLanguage === 'ru' ? "👥 От рефералов: {$referralEarnings} монет" : "👥 From referrals: {$referralEarnings} coins";
        $withdrawalsCountText = $userLanguage === 'ru' ? "💸 Выводов: " . $withdrawalsCount : "💸 Withdrawals: " . $withdrawalsCount;

        $text = $yourBalance . "\n\n";
        $text .= $currentBalanceText . "\n";
        $text .= $balanceUsdText . "\n\n";
        $text .= $totalEarnedText . "\n";
        $text .= $fromReferralsText . "\n";
        $text .= $withdrawalsCountText . "\n\n";
    }
    
    // Показываем последние 3 выплаты
    if (!empty($user['withdrawals'])) {
        $lastWithdrawalsTitle = getBotTextFromFile('system.last_withdrawals', $userLanguage) ?:
                               ($userLanguage === 'ru' ? "📜 <b>Последние выплаты:</b>" : "📜 <b>Recent withdrawals:</b>");
        $text .= $lastWithdrawalsTitle . "\n";
        $withdrawals = array_slice($user['withdrawals'], -3); // Последние 3 выплаты

        foreach ($withdrawals as $withdrawal) {
            $status = $withdrawal['status'] ?? 'pending';
            $amount = $withdrawal['coins_amount'] ?? 0;
            $currency = strtoupper($withdrawal['currency'] ?? '');
            $date = $withdrawal['created_at'] ?? date('Y-m-d', time());

            // Переводим статус
            $statusText = $status;
            if ($status === 'completed') {
                $statusText = getBotTextFromFile('system.withdrawal_status_completed', $userLanguage) ?:
                             ($userLanguage === 'ru' ? 'Завершено' : 'Completed');
            } elseif ($status === 'pending') {
                $statusText = getBotTextFromFile('system.withdrawal_status_pending', $userLanguage) ?:
                             ($userLanguage === 'ru' ? 'В обработке' : 'Processing');
            } elseif ($status === 'failed') {
                $statusText = getBotTextFromFile('system.withdrawal_status_failed', $userLanguage) ?:
                             ($userLanguage === 'ru' ? 'Ошибка' : 'Failed');
            }

            $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';
            $text .= "→ {$amount} {$coinsText} ({$currency}) - {$statusText} ({$date})\n";
        }
        $text .= "\n";
    }
    
    $openAppWithdrawText = getBotTextFromFile('balance.open_app_withdraw', $userLanguage) ?:
                          ($userLanguage === 'ru' ? "🚀 Откройте приложение для вывода средств!" : "🚀 Open the app to withdraw funds!");
    $text .= $openAppWithdrawText;

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "🚀 Открыть приложение" : "🚀 Open App"),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.back', $userLanguage) ?:
                              ($userLanguage === 'ru' ? "🔙 Назад" : "🔙 Back"),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка приглашения друзей
 */
function handleInviteFriends($chatId, $userId) {
    $userData = loadUserData();
    $userLanguage = getCurrentUserLanguage($userId);

    if (!isset($userData[$userId])) {
        $errorText = getBotTextFromFile('common.user_not_found', $userLanguage) ?: "❌ Пользователь не найден. Отправьте /start для регистрации.";
        sendMessage($chatId, $errorText);
        return;
    }

    $user = $userData[$userId];
    $referrals = $user['referrals'] ?? [];
    $referralCount = count($referrals);
    $referralEarnings = $user['referral_earnings'] ?? 0;

    $referralLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";

    // Пробуем получить тексты из новой структуры
    $programTitle = getBotTextFromFile('referrals.program_title', $userLanguage);
    $bonusText = getBotTextFromFile('referrals.bonus_text', $userLanguage, ['percent' => REFERRAL_BONUS_PERCENT]);
    $statsTitle = getBotTextFromFile('referrals.stats_title', $userLanguage);
    $invitedText = getBotTextFromFile('referrals.invited_friends', $userLanguage, ['count' => $referralCount]);
    $earnedText = getBotTextFromFile('referrals.earned_from_referrals', $userLanguage, ['amount' => $referralEarnings]);
    $linkTitle = getBotTextFromFile('referrals.your_referral_link', $userLanguage);
    $shareText = getBotTextFromFile('referrals.share_with_friends', $userLanguage);

    if ($programTitle) {
        // Строим текст из новой структуры
        $text = "<b>{$programTitle}</b>\n\n";
        if ($bonusText) $text .= "{$bonusText}\n\n";
        if ($statsTitle) $text .= "<b>{$statsTitle}</b>\n";
        if ($invitedText) $text .= "{$invitedText}\n";
        if ($earnedText) $text .= "{$earnedText}\n\n";
        if ($linkTitle) $text .= "<b>{$linkTitle}</b>\n";
        $text .= "<code>{$referralLink}</code>\n\n";
        if ($shareText) $text .= "{$shareText}";
    } else {
        // Fallback с правильными многоязычными текстами
        $programTitle = $userLanguage === 'ru' ? "<b>👥 Реферальная программа</b>" : "<b>👥 Referral Program</b>";
        $bonusText = $userLanguage === 'ru' ?
                    "🎁 Получайте " . REFERRAL_BONUS_PERCENT . "% от заработка каждого приглашенного друга!" :
                    "🎁 Get " . REFERRAL_BONUS_PERCENT . "% from each invited friend's earnings!";
        $statsTitle = $userLanguage === 'ru' ? "<b>📊 Ваша статистика:</b>" : "<b>📊 Your Statistics:</b>";
        $invitedText = $userLanguage === 'ru' ? "👥 Приглашено друзей: {$referralCount}" : "👥 Friends invited: {$referralCount}";
        $earnedText = $userLanguage === 'ru' ? "💰 Заработано с рефералов: {$referralEarnings} монет" : "💰 Earned from referrals: {$referralEarnings} coins";
        $linkTitle = $userLanguage === 'ru' ? "<b>🔗 Ваша реферальная ссылка:</b>" : "<b>🔗 Your Referral Link:</b>";
        $shareText = $userLanguage === 'ru' ? "📤 Поделитесь ссылкой с друзьями и зарабатывайте вместе!" : "📤 Share the link with friends and earn together!";

        $text = $programTitle . "\n\n";
        $text .= $bonusText . "\n\n";
        $text .= $statsTitle . "\n";
        $text .= $invitedText . "\n";
        $text .= $earnedText . "\n\n";
        $text .= $linkTitle . "\n";
        $text .= "<code>{$referralLink}</code>\n\n";
        $text .= $shareText;
    }

    $shareButtonText = getBotTextFromFile('buttons.share_link', $userLanguage) ?:
                      ($userLanguage === 'ru' ? '📤 Поделиться ссылкой' : '📤 Share Link');
    $openAppText = getBotTextFromFile('buttons.open_app', $userLanguage) ?:
                  ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App');
    $backText = getBotTextFromFile('buttons.back', $userLanguage) ?:
               ($userLanguage === 'ru' ? '🔙 Назад' : '🔙 Back');
    $shareMessage = getBotTextFromFile('referrals.share_text', $userLanguage) ?:
                   ($userLanguage === 'ru' ? "Просто смотри рекламу и получай крипту на карту. Мгновенный вывод на кошелёк:" : "Just watch ads and get crypto to your card. Instant withdrawal to wallet:");

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => $shareButtonText,
                    'switch_inline_query' => "{$shareMessage} {$referralLink}"
                ]
            ],
            [
                [
                    'text' => $openAppText,
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => $backText,
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка команды статистики
 */
function handleStatsCommand($chatId, $userId) {
    $userData = loadUserData();
    $userLanguage = getCurrentUserLanguage($userId);

    if (!isset($userData[$userId])) {
        $errorText = getBotTextFromFile('common.user_not_found', $userLanguage) ?: "❌ Пользователь не найден. Отправьте /start для регистрации.";
        sendMessage($chatId, $errorText);
        return;
    }

    $user = $userData[$userId];
    $balance = $user['balance'] ?? 0;
    $totalEarned = $user['total_earned'] ?? 0;
    $referralEarnings = $user['referral_earnings'] ?? 0;
    $withdrawalsCount = $user['withdrawals_count'] ?? 0;
    $referrals = $user['referrals'] ?? [];
    $registeredAt = $user['registered_at'] ?? time();

    $daysActive = max(1, floor((time() - $registeredAt) / 86400));
    $avgPerDay = round($totalEarned / $daysActive, 2);

    $referralLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";

    // Пробуем построить текст статистики из новой структуры
    $title = getBotTextFromFile('statistics.title', $userLanguage);
    $currentBalanceText = getBotTextFromFile('statistics.current_balance', $userLanguage, ['balance' => $balance]);
    $totalEarnedText = getBotTextFromFile('statistics.total_earned', $userLanguage, ['amount' => $totalEarned]);
    $fromReferralsText = getBotTextFromFile('statistics.from_referrals', $userLanguage, ['amount' => $referralEarnings]);
    $withdrawalsCountText = getBotTextFromFile('statistics.withdrawals_count', $userLanguage, ['count' => $withdrawalsCount]);
    $daysActiveText = getBotTextFromFile('statistics.days_active', $userLanguage, ['days' => $daysActive]);
    $avgPerDayText = getBotTextFromFile('statistics.average_per_day', $userLanguage, ['amount' => $avgPerDay]);
    $invitedFriendsText = getBotTextFromFile('statistics.invited_friends', $userLanguage, ['count' => count($referrals)]);
    $registrationDateText = getBotTextFromFile('statistics.registration_date', $userLanguage, ['date' => date('d.m.Y', $registeredAt)]);

    if ($title) {
        // Строим текст из новой структуры
        $text = "<b>{$title}</b>\n\n";
        if ($currentBalanceText) {
            $text .= $currentBalanceText . "\n";
        } else {
            $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';
            $text .= "🪙 " . ($userLanguage === 'ru' ? 'Текущий баланс' : 'Current balance') . ": <b>{$balance} {$coinsText}</b>\n";
        }

        if ($totalEarnedText) {
            $text .= $totalEarnedText . "\n";
        } else {
            $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';
            $text .= "📈 " . ($userLanguage === 'ru' ? 'Всего заработано' : 'Total earned') . ": <b>{$totalEarned} {$coinsText}</b>\n";
        }

        if ($fromReferralsText) {
            $text .= $fromReferralsText . "\n";
        } else {
            $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';
            $text .= "👥 " . ($userLanguage === 'ru' ? 'От рефералов' : 'From referrals') . ": <b>{$referralEarnings} {$coinsText}</b>\n";
        }

        if ($withdrawalsCountText) {
            $text .= $withdrawalsCountText . "\n\n";
        } else {
            $text .= "💸 " . ($userLanguage === 'ru' ? 'Выводов сделано' : 'Withdrawals made') . ": <b>{$withdrawalsCount}</b>\n\n";
        }

        if ($daysActiveText) {
            $text .= $daysActiveText . "\n";
        } else {
            $text .= "📅 " . ($userLanguage === 'ru' ? 'Дней в системе' : 'Days in system') . ": <b>{$daysActive}</b>\n";
        }

        if ($avgPerDayText) {
            $text .= $avgPerDayText . "\n";
        } else {
            $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';
            $text .= "⚡ " . ($userLanguage === 'ru' ? 'Среднее в день' : 'Average per day') . ": <b>{$avgPerDay} {$coinsText}</b>\n";
        }

        if ($invitedFriendsText) {
            $text .= $invitedFriendsText . "\n\n";
        } else {
            $text .= "👥 " . ($userLanguage === 'ru' ? 'Приглашено друзей' : 'Friends invited') . ": <b>" . count($referrals) . "</b>\n\n";
        }

        if ($registrationDateText) {
            $text .= $registrationDateText;
        } else {
            $text .= "📅 " . ($userLanguage === 'ru' ? 'Дата регистрации' : 'Registration date') . ": <b>" . date('d.m.Y', $registeredAt) . "</b>";
        }
    } else {
        // Fallback с правильными многоязычными текстами
        $statsTitle = $userLanguage === 'ru' ? "📊 <b>Ваша статистика</b>" : "📊 <b>Your Statistics</b>";
        $coinsText = $userLanguage === 'ru' ? 'монет' : 'coins';

        $text = $statsTitle . "\n\n";
        $text .= "🪙 " . ($userLanguage === 'ru' ? 'Текущий баланс' : 'Current balance') . ": <b>{$balance} {$coinsText}</b>\n";
        $text .= "📈 " . ($userLanguage === 'ru' ? 'Всего заработано' : 'Total earned') . ": <b>{$totalEarned} {$coinsText}</b>\n";
        $text .= "👥 " . ($userLanguage === 'ru' ? 'От рефералов' : 'From referrals') . ": <b>{$referralEarnings} {$coinsText}</b>\n";
        $text .= "💸 " . ($userLanguage === 'ru' ? 'Выводов сделано' : 'Withdrawals made') . ": <b>{$withdrawalsCount}</b>\n\n";
        $text .= "📅 " . ($userLanguage === 'ru' ? 'Дней в системе' : 'Days in system') . ": <b>{$daysActive}</b>\n";
        $text .= "⚡ " . ($userLanguage === 'ru' ? 'Среднее в день' : 'Average per day') . ": <b>{$avgPerDay} {$coinsText}</b>\n";
        $text .= "👥 " . ($userLanguage === 'ru' ? 'Приглашено друзей' : 'Friends invited') . ": <b>" . count($referrals) . "</b>\n\n";
        $text .= "📅 " . ($userLanguage === 'ru' ? 'Дата регистрации' : 'Registration date') . ": <b>" . date('d.m.Y', $registeredAt) . "</b>";
    }

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?:
                              ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App'),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.back', $userLanguage) ?:
                              ($userLanguage === 'ru' ? '🔙 Назад' : '🔙 Back'),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Обработка команды помощи
 */
function handleHelpCommand($chatId, $userId = null) {
    // Получаем язык пользователя
    $userLanguage = getCurrentUserLanguage($userId);

    // Пробуем построить текст помощи из новой структуры
    $title = getBotTextFromFile('help.title', $userLanguage);
    $description = getBotTextFromFile('help.description', $userLanguage);
    $howToUse = getBotTextFromFile('help.how_to_use', $userLanguage);
    $step1 = getBotTextFromFile('help.step1', $userLanguage);
    $step2 = getBotTextFromFile('help.step2', $userLanguage);
    $step3 = getBotTextFromFile('help.step3', $userLanguage);
    $step4 = getBotTextFromFile('help.step4', $userLanguage);
    $rateInfo = getBotTextFromFile('help.rate_info', $userLanguage);
    $referralBonus = getBotTextFromFile('help.referral_bonus', $userLanguage);
    $instantPayouts = getBotTextFromFile('help.instant_payouts', $userLanguage);

    if ($title) {
        // Строим текст из новой структуры
        $text = "<b>{$title}</b>\n\n";
        if ($description) $text .= $description . "\n\n";
        if ($howToUse) $text .= "<b>{$howToUse}</b>\n";
        if ($step1) $text .= $step1 . "\n";
        if ($step2) $text .= $step2 . "\n";
        if ($step3) $text .= $step3 . "\n";
        if ($step4) $text .= $step4 . "\n\n";
        if ($rateInfo) $text .= $rateInfo . "\n";
        if ($referralBonus) $text .= $referralBonus . "\n";
        if ($instantPayouts) $text .= $instantPayouts;
    } else {
        // Fallback с правильными многоязычными текстами
        if ($userLanguage === 'ru') {
            $text = "❓ <b>Помощь по UniQPaid</b>\n\n";
            $text .= "🎯 <b>Как зарабатывать:</b>\n";
            $text .= "• Откройте мини-приложение\n";
            $text .= "• Смотрите рекламу и получайте до 10 монет за каждый просмотр\n";
            $text .= "• Приглашайте друзей и получайте 10% от их заработка\n\n";
            $text .= "💰 <b>Как выводить:</b>\n";
            $text .= "• Накопите монеты\n";
            $text .= "• Откройте приложение\n";
            $text .= "• Перейдите в раздел 'Заработок'\n";
            $text .= "• Запросите вывод на криптокошелек\n\n";
            $text .= "💎 <b>Курс:</b> 10 монет = $0.01 USD";
        } else {
            $text = "❓ <b>UniQPaid Help</b>\n\n";
            $text .= "🎯 <b>How to earn:</b>\n";
            $text .= "• Open the mini-app\n";
            $text .= "• Watch ads and get up to 10 coins for each view\n";
            $text .= "• Invite friends and get 10% from their earnings\n\n";
            $text .= "💰 <b>How to withdraw:</b>\n";
            $text .= "• Accumulate coins\n";
            $text .= "• Open the app\n";
            $text .= "• Go to 'Earnings' section\n";
            $text .= "• Request withdrawal to crypto wallet\n\n";
            $text .= "💎 <b>Rate:</b> 10 coins = $0.01 USD";
        }
    }

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?:
                              ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App'),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.back', $userLanguage) ?:
                              ($userLanguage === 'ru' ? '🔙 Назад' : '🔙 Back'),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Создание текста подробной информации из переводов
 */
function createDetailedInfoText($userLanguage) {
    $text = "<b>" . getBotTextFromFile('detailed_info.title', $userLanguage) . "</b>\n\n";

    $text .= "<b>" . getBotTextFromFile('detailed_info.limitations_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('detailed_info.limitations_links', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.limitations_banners', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.limitations_videos', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('detailed_info.rewards_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('detailed_info.rewards_banner', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.rewards_video', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.rewards_link', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('detailed_info.referral_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('detailed_info.referral_bonus', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.referral_lifetime', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.referral_unlimited', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('detailed_info.withdrawal_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('detailed_info.withdrawal_minimum', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.withdrawal_currencies', $userLanguage) . "\n";
    $text .= getBotTextFromFile('detailed_info.withdrawal_instant', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('detailed_info.blockchain_warning_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('detailed_info.blockchain_warning_text', $userLanguage) . "\n\n";
    $text .= getBotTextFromFile('detailed_info.blockchain_warning_docs', $userLanguage);

    return $text;
}

/**
 * Обработка подробной информации
 */
function handleDetailedInfo($chatId, $userId) {
    $userLanguage = getCurrentUserLanguage($userId);
    $text = createDetailedInfoText($userLanguage);

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?: ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App'),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.back', $userLanguage) ?: ($userLanguage === 'ru' ? '🔙 Назад' : '🔙 Back'),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}

/**
 * Создание текста "Как это работает" из переводов
 */
function createHowItWorksText($userLanguage) {
    $text = "<b>" . getBotTextFromFile('how_it_works.title', $userLanguage) . "</b>\n\n";

    $text .= "<b>" . getBotTextFromFile('how_it_works.step1_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('how_it_works.step1_start', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step1_auto', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step1_balance', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('how_it_works.step2_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('how_it_works.step2_open', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step2_choose', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step2_push', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step2_watch', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step2_tap', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step2_complete', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('how_it_works.step3_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('how_it_works.step3_share', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step3_earn', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step3_auto', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('how_it_works.step4_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('how_it_works.step4_accumulate', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step4_choose', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step4_address', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.step4_instant', $userLanguage) . "\n\n";

    $text .= "<b>" . getBotTextFromFile('how_it_works.exchange_title', $userLanguage) . "</b>\n";
    $text .= getBotTextFromFile('how_it_works.exchange_1000', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.exchange_10', $userLanguage) . "\n";
    $text .= getBotTextFromFile('how_it_works.exchange_1', $userLanguage);

    return $text;
}

/**
 * Обработка "Как это работает"
 */
function handleHowItWorks($chatId, $userId) {
    $userLanguage = getCurrentUserLanguage($userId);
    $text = createHowItWorksText($userLanguage);

    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => getBotTextFromFile('buttons.open_app', $userLanguage) ?: ($userLanguage === 'ru' ? '🚀 Открыть приложение' : '🚀 Open App'),
                    'web_app' => ['url' => WEBAPP_URL]
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.detailed_info', $userLanguage) ?: ($userLanguage === 'ru' ? '📖 Подробная информация' : '📖 Detailed Information'),
                    'callback_data' => 'detailed_info'
                ]
            ],
            [
                [
                    'text' => getBotTextFromFile('buttons.back', $userLanguage) ?: ($userLanguage === 'ru' ? '🔙 Назад' : '🔙 Back'),
                    'callback_data' => 'main_menu'
                ]
            ]
        ]
    ];

    sendMessage($chatId, $text, $keyboard);
}
?>