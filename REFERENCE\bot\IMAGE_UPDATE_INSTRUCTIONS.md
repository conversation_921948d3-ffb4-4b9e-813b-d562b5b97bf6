# 🔄 Инструкция по обновлению картинки бота

## 🎯 Быстрое обновление

### Шаг 1: Загрузите новую картинку
1. Убедитесь, что картинка в PNG формате
2. Назовите файл `bot_welcome_super_banner.png`
3. Загрузите в папку `images/` на сервер
4. Убедитесь, что файл доступен по URL: https://app.uniqpaid.com/test2/images/bot_welcome_super_banner.png

### Шаг 2: Получите ваш chat_id
1. Отправьте любое сообщение боту @uniqpaid_paid_bot
2. Посмотрите логи в файле `bot/bot.log`
3. Найдите строку типа: `INFO: Сообщение от пользователя 123456789`
4. Чи<PERSON>ло `123456789` - это ваш chat_id

### Шаг 3: Обновите картинку через webhook
Откройте в браузере (замените YOUR_CHAT_ID на ваш реальный chat_id):

```
https://app.uniqpaid.com/test2/bot/update_image.php?chat_id=YOUR_CHAT_ID
```

Например:
```
https://app.uniqpaid.com/test2/bot/update_image.php?chat_id=123456789
```

### Шаг 4: Проверьте результат
1. Если увидите "✅ Успех!" - картинка обновлена
2. Протестируйте бота командой `/start`
3. Новая картинка должна появиться в приветственном сообщении

## 🛠️ Расширенные возможности

### Полный набор инструментов
Откройте: https://app.uniqpaid.com/test2/bot/reload_image.php

Доступные действия:
- 🔄 **Перезагрузить картинку** - основное обновление
- 🆔 **Получить file_id** - для оптимизации (можно использовать file_id вместо URL)
- 🗑️ **Очистить кэш** - принудительное обновление кэша
- 🧪 **Тест доступности** - проверка доступности картинки

### Параметры URL

```
?action=reload&chat_id=YOUR_CHAT_ID     - перезагрузить картинку
?action=get_file_id&chat_id=YOUR_CHAT_ID - получить file_id
?action=clear_cache                      - очистить кэш
?action=test                            - тест доступности
```

## 🔧 Устранение проблем

### Картинка не отправляется
1. **Проверьте формат** - должен быть PNG, JPEG, GIF или WEBP (НЕ SVG!)
2. **Проверьте размер** - не более 10MB
3. **Проверьте URL** - откройте https://app.uniqpaid.com/test2/images/bot_welcome_banner.png в браузере
4. **Проверьте chat_id** - убедитесь, что используете правильный ID

### Ошибка "Bad Request"
- Неверный chat_id
- Неподдерживаемый формат файла
- Слишком большой размер файла

### Картинка не обновляется
- Очистите кэш браузера
- Используйте параметр `?action=clear_cache`
- Добавьте timestamp к URL картинки

## 💡 Советы

### Оптимальные параметры картинки
- **Формат**: PNG или JPEG
- **Размер**: 800x400px или 1200x600px
- **Вес**: до 1MB для быстрой загрузки
- **Качество**: высокое, но сжатое

### Использование file_id
После первой успешной загрузки можно использовать file_id вместо URL:

```php
// Вместо URL используем file_id
$result = sendPhoto($chatId, 'AgACAgIAAxkBAAI...', $message, $keyboard);
```

Это быстрее и надежнее, так как файл уже загружен в Telegram.

## 🚀 Автоматизация

Можно создать скрипт для автоматического обновления:

```bash
# Загрузка новой картинки
curl -X POST "https://app.uniqpaid.com/test2/bot/update_image.php?chat_id=YOUR_CHAT_ID"
```

Или добавить в админ-панель кнопку "Обновить картинку бота".

## 📞 Поддержка

Если что-то не работает:
1. Проверьте логи в `bot/bot.log`
2. Убедитесь, что webhook активен
3. Проверьте доступность картинки по прямому URL
4. Убедитесь, что бот не заблокирован пользователем
