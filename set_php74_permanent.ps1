# Установка PHP 7.4 как основной версии
Write-Host "Устанавливаем PHP 7.4 как основную версию..." -ForegroundColor Green
[Environment]::SetEnvironmentVariable('PATH', 'D:\OSPanel\modules\php\PHP_7.4;' + [Environment]::GetEnvironmentVariable('PATH', 'User'), 'User')
Write-Host "✅ PHP 7.4 установлен как основная версия" -ForegroundColor Green
Write-Host "🎉 SQLite будет работать идеально!" -ForegroundColor Cyan