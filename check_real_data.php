<?php
// Проверка реальных данных в базе
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Проверка реальных данных</h1>";

Database::init();

// Проверяем данные в каждой таблице
echo "<h2>📊 Таблица settings</h2>";
try {
    $settings = Database::query("SELECT * FROM settings LIMIT 10");
    echo "<p>Записей в settings: " . count($settings) . "</p>";
    if (count($settings) > 0) {
        echo "<h3>Первые 5 записей:</h3>";
        for ($i = 0; $i < min(5, count($settings)); $i++) {
            $setting = $settings[$i];
            echo "<p>- {$setting['category']}.{$setting['key']} = " . substr($setting['value'], 0, 50) . "</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Таблица settings пустая!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h2>👥 Таблица users</h2>";
try {
    $users = Database::query("SELECT * FROM users LIMIT 10");
    echo "<p>Записей в users: " . count($users) . "</p>";
    if (count($users) > 0) {
        echo "<h3>Первые 5 записей:</h3>";
        for ($i = 0; $i < min(5, count($users)); $i++) {
            $user = $users[$i];
            echo "<p>- {$user['telegram_id']}: {$user['first_name']} {$user['last_name']}</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Таблица users пустая!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h2>📝 Таблица texts</h2>";
try {
    $texts = Database::query("SELECT * FROM texts LIMIT 5");
    echo "<p>Записей в texts: " . count($texts) . "</p>";
    if (count($texts) > 0) {
        echo "<h3>Примеры:</h3>";
        foreach ($texts as $text) {
            echo "<p>- {$text['key']} ({$text['language']})</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Проверяем структуру таблиц
echo "<h2>🏗️ Структура таблиц</h2>";

echo "<h3>settings:</h3>";
try {
    $structure = Database::query("PRAGMA table_info(settings)");
    foreach ($structure as $col) {
        echo "<p>- {$col['name']} ({$col['type']})</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>users:</h3>";
try {
    $structure = Database::query("PRAGMA table_info(users)");
    foreach ($structure as $col) {
        echo "<p>- {$col['name']} ({$col['type']})</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Проверяем, есть ли резервные копии
echo "<h2>💾 Резервные копии</h2>";
$backupDir = 'database';
$backupFiles = glob($backupDir . '/backup_json_*');
echo "<p>Найдено резервных папок: " . count($backupFiles) . "</p>";
foreach ($backupFiles as $backup) {
    echo "<p>- " . basename($backup) . "</p>";
    $files = glob($backup . '/*.json');
    foreach ($files as $file) {
        $size = filesize($file);
        echo "<p>&nbsp;&nbsp;- " . basename($file) . " ($size байт)</p>";
    }
}

echo "<h2>🎯 ДИАГНОЗ</h2>";
echo "<p style='color: red; font-weight: bold;'>ПРОБЛЕМА: Таблицы settings и users пустые!</p>";
echo "<p>Нужно восстановить данные из резервных копий или заново импортировать.</p>";

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
