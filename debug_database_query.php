<?php
// Отладка Database::query
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Отладка Database::query</h1>";

Database::init();

// Тест 1: Простой запрос без параметров
echo "<h2>1️⃣ Простой запрос</h2>";
try {
    $result = Database::query("SELECT * FROM settings LIMIT 3");
    echo "<p>Результат: " . gettype($result) . "</p>";
    echo "<p>Количество: " . (is_array($result) ? count($result) : 'не массив') . "</p>";
    if (is_array($result) && count($result) > 0) {
        echo "<h3>Первая запись:</h3>";
        echo "<pre>" . print_r($result[0], true) . "</pre>";
    } else {
        echo "<p style='color: red'>❌ Пустой результат!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 2: Запрос с параметрами (как в модели)
echo "<h2>2️⃣ Запрос с параметрами</h2>";
try {
    $result = Database::query("SELECT * FROM settings WHERE category = ? ORDER BY key ASC", ['miniapp']);
    echo "<p>Результат: " . gettype($result) . "</p>";
    echo "<p>Количество: " . (is_array($result) ? count($result) : 'не массив') . "</p>";
    if (is_array($result) && count($result) > 0) {
        echo "<h3>Первая запись:</h3>";
        echo "<pre>" . print_r($result[0], true) . "</pre>";
    } else {
        echo "<p style='color: red'>❌ Пустой результат для category='miniapp'!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 3: Проверяем, какие категории есть в базе
echo "<h2>3️⃣ Проверка категорий</h2>";
try {
    $result = Database::query("SELECT DISTINCT category FROM settings");
    echo "<p>Найденные категории:</p>";
    if (is_array($result)) {
        foreach ($result as $row) {
            echo "<p>- " . ($row['category'] ?? 'NULL') . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 4: Проверяем количество записей по категориям
echo "<h2>4️⃣ Количество по категориям</h2>";
$categories = ['bot', 'miniapp', 'nowpayments'];
foreach ($categories as $category) {
    try {
        $result = Database::query("SELECT COUNT(*) as cnt FROM settings WHERE category = ?", [$category]);
        $count = $result[0]['cnt'] ?? 'ERROR';
        echo "<p>$category: $count записей</p>";
    } catch (Exception $e) {
        echo "<p style='color: red'>❌ Ошибка $category: " . $e->getMessage() . "</p>";
    }
}

// Тест 5: Проверяем метод Database::execute
echo "<h2>5️⃣ Тест Database::execute</h2>";
try {
    $result = Database::execute("SELECT 1");
    echo "<p>Database::execute вернул: " . gettype($result) . " = $result</p>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка execute: " . $e->getMessage() . "</p>";
}

// Тест 6: Проверяем подключение к базе
echo "<h2>6️⃣ Проверка подключения</h2>";
try {
    // Используем рефлексию для доступа к приватным свойствам
    $reflection = new ReflectionClass('App\Core\Database');
    if ($reflection->hasProperty('pdo')) {
        $pdoProperty = $reflection->getProperty('pdo');
        $pdoProperty->setAccessible(true);
        $pdo = $pdoProperty->getValue();
        
        if ($pdo) {
            echo "<p>✅ PDO подключение активно</p>";
            echo "<p>Драйвер: " . $pdo->getAttribute(PDO::ATTR_DRIVER_NAME) . "</p>";
        } else {
            echo "<p style='color: red'>❌ PDO подключение отсутствует</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка проверки подключения: " . $e->getMessage() . "</p>";
}

echo "<h2>🎯 Диагноз</h2>";
echo "<p>Если простой запрос работает, а запрос с параметрами нет - проблема в параметрах.</p>";
echo "<p>Если все запросы пустые - проблема в Database::query().</p>";

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
