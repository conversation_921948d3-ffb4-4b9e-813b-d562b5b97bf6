<?php
// Проверка базы данных в веб-интерфейсе
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Проверка базы данных в веб-интерфейсе</h1>";

Database::init();

// Проверяем, какую базу использует веб-интерфейс
echo "<h2>📊 Информация о базе данных</h2>";

// Проверяем константы
echo "<h3>Константы:</h3>";
echo "<p>DATABASE_PATH: " . (defined('DATABASE_PATH') ? DATABASE_PATH : 'НЕ ОПРЕДЕЛЕНО') . "</p>";
echo "<p>DEV_MODE: " . (defined('DEV_MODE') ? (DEV_MODE ? 'true' : 'false') : 'НЕ ОПРЕДЕЛЕНО') . "</p>";

// Проверяем файлы баз данных
echo "<h3>Файлы баз данных:</h3>";
$dbFiles = [
    'database/app.sqlite',
    'database/app_dev.sqlite',
    'app.sqlite',
    'app_dev.sqlite'
];

foreach ($dbFiles as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    echo "<p>$file: " . ($exists ? "✅ ($size байт)" : "❌ НЕ НАЙДЕН") . "</p>";
}

// Проверяем содержимое через Database::query
echo "<h2>📊 Содержимое через Database::query</h2>";

echo "<h3>Таблица settings:</h3>";
try {
    $settings = Database::query("SELECT * FROM settings LIMIT 5");
    echo "<p>Записей: " . count($settings) . "</p>";
    
    if (count($settings) > 0) {
        echo "<h4>Первые записи:</h4>";
        foreach ($settings as $setting) {
            echo "<p>- {$setting['category']}.{$setting['key']} = " . substr($setting['value'], 0, 30) . "</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Таблица пустая!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>Таблица users:</h3>";
try {
    $usersCount = Database::query("SELECT COUNT(*) as cnt FROM users");
    $totalUsers = $usersCount[0]['cnt'] ?? 0;
    echo "<p>Записей: $totalUsers</p>";
    
    if ($totalUsers > 0) {
        $users = Database::query("SELECT * FROM users LIMIT 3");
        echo "<h4>Первые пользователи:</h4>";
        foreach ($users as $user) {
            echo "<p>- {$user['telegram_id']}: {$user['first_name']} {$user['last_name']}</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Таблица пустая!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>Таблица admin_users:</h3>";
try {
    $admins = Database::query("SELECT * FROM admin_users");
    echo "<p>Записей: " . count($admins) . "</p>";
    
    if (count($admins) > 0) {
        foreach ($admins as $admin) {
            echo "<p>- {$admin['username']} (ID: {$admin['id']})</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Таблица пустая!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Проверяем расширения PHP
echo "<h2>🔧 Расширения PHP</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? 'YES ✅' : 'NO ❌') . "</p>";
echo "<p>PDO SQLite: " . (extension_loaded('pdo_sqlite') ? 'YES ✅' : 'NO ❌') . "</p>";
echo "<p>SQLite3: " . (extension_loaded('sqlite3') ? 'YES ✅' : 'NO ❌') . "</p>";

echo "<h2>🎯 Диагноз</h2>";
echo "<p>Если в веб-интерфейсе данные есть, а в терминале нет - используются разные базы!</p>";

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
