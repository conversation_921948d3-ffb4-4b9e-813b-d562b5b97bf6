<?php
echo "=== ПРОВЕРКА БАЗЫ ДАННЫХ В ТЕРМИНАЛЕ ===\n\n";

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

Database::init();

echo "✅ База данных инициализирована\n\n";

// Проверяем файл базы данных
$dbFile = 'database/app.sqlite';
echo "📁 ФАЙЛ БАЗЫ ДАННЫХ:\n";
echo "Путь: $dbFile\n";
echo "Существует: " . (file_exists($dbFile) ? 'ДА' : 'НЕТ') . "\n";
echo "Размер: " . filesize($dbFile) . " байт\n";
echo "Права: " . substr(sprintf('%o', fileperms($dbFile)), -4) . "\n\n";

// Прямое подключение к SQLite через PDO
echo "🔗 ПРЯМОЕ ПОДКЛЮЧЕНИЕ К SQLITE:\n";
try {
    $pdo = new PDO("sqlite:$dbFile");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Прямое PDO подключение успешно\n";
    
    // Проверяем таблицы
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_ASSOC);
    echo "Таблицы в базе: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "  - {$table['name']}\n";
    }
    
    // Проверяем данные в таблице settings
    echo "\n📊 ДАННЫЕ В ТАБЛИЦЕ SETTINGS:\n";
    $settings = $pdo->query("SELECT * FROM settings LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
    echo "Записей в settings: " . count($settings) . "\n";
    
    if (count($settings) > 0) {
        echo "Первые 3 записи:\n";
        for ($i = 0; $i < min(3, count($settings)); $i++) {
            $setting = $settings[$i];
            echo "  - {$setting['category']}.{$setting['key']} = " . substr($setting['value'], 0, 30) . "\n";
        }
    } else {
        echo "❌ Таблица settings ПУСТАЯ!\n";
    }
    
    // Проверяем данные в таблице users
    echo "\n👥 ДАННЫЕ В ТАБЛИЦЕ USERS:\n";
    $users = $pdo->query("SELECT COUNT(*) as cnt FROM users")->fetch(PDO::FETCH_ASSOC);
    echo "Записей в users: " . $users['cnt'] . "\n";
    
    if ($users['cnt'] > 0) {
        $userExamples = $pdo->query("SELECT telegram_id, first_name, last_name FROM users LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
        echo "Первые 3 пользователя:\n";
        foreach ($userExamples as $user) {
            echo "  - {$user['telegram_id']}: {$user['first_name']} {$user['last_name']}\n";
        }
    } else {
        echo "❌ Таблица users ПУСТАЯ!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка прямого подключения: " . $e->getMessage() . "\n";
}

// Проверяем Database::query через наш класс
echo "\n🔧 ПРОВЕРКА Database::query:\n";
try {
    $result = Database::query("SELECT name FROM sqlite_master WHERE type='table'");
    echo "Database::query вернул: " . gettype($result) . "\n";
    echo "Количество: " . (is_array($result) ? count($result) : 'не массив') . "\n";
    
    if (is_array($result) && count($result) > 0) {
        echo "Таблицы через Database::query:\n";
        foreach ($result as $table) {
            echo "  - " . ($table['name'] ?? 'NULL') . "\n";
        }
    } else {
        echo "❌ Database::query возвращает пустой массив!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка Database::query: " . $e->getMessage() . "\n";
}

// Проверяем конфигурацию
echo "\n⚙️ КОНФИГУРАЦИЯ:\n";
echo "DATABASE_PATH: " . (defined('DATABASE_PATH') ? DATABASE_PATH : 'НЕ ОПРЕДЕЛЕНО') . "\n";
echo "DEV_MODE: " . (defined('DEV_MODE') ? (DEV_MODE ? 'true' : 'false') : 'НЕ ОПРЕДЕЛЕНО') . "\n";

echo "\n🎯 ДИАГНОЗ:\n";
echo "Если прямое PDO работает, а Database::query нет - проблема в классе Database\n";
echo "Если и прямое PDO показывает пустые таблицы - база данных пустая\n";

echo "\n=== КОНЕЦ ПРОВЕРКИ ===\n";
?>
