<?php
// Тест данных контроллера
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;
use App\Models\Setting;
use App\Models\User;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🎯 Тест данных контроллера</h1>";

Database::init();

// Имитируем то, что делает контроллер настроек
echo "<h2>📊 Тест контроллера настроек</h2>";

echo "<h3>1. Setting::getFormattedByCategory('miniapp')</h3>";
try {
    $miniappSettings = Setting::getFormattedByCategory('miniapp');
    echo "<p>Количество: " . count($miniappSettings) . "</p>";
    if (count($miniappSettings) > 0) {
        echo "<h4>Первые 3 настройки:</h4>";
        for ($i = 0; $i < min(3, count($miniappSettings)); $i++) {
            $setting = $miniappSettings[$i];
            echo "<p>- {$setting['key']}: " . substr($setting['value'], 0, 50) . "</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Пустой массив!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>2. Setting::getFormattedByCategory('bot')</h3>";
try {
    $botSettings = Setting::getFormattedByCategory('bot');
    echo "<p>Количество: " . count($botSettings) . "</p>";
    if (count($botSettings) > 0) {
        echo "<h4>Первые 3 настройки:</h4>";
        for ($i = 0; $i < min(3, count($botSettings)); $i++) {
            $setting = $botSettings[$i];
            echo "<p>- {$setting['key']}: " . substr($setting['value'], 0, 50) . "</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Пустой массив!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>3. Setting::getFormattedByCategory('nowpayments')</h3>";
try {
    $nowpaymentsSettings = Setting::getFormattedByCategory('nowpayments');
    echo "<p>Количество: " . count($nowpaymentsSettings) . "</p>";
    if (count($nowpaymentsSettings) > 0) {
        echo "<h4>Первые 3 настройки:</h4>";
        for ($i = 0; $i < min(3, count($nowpaymentsSettings)); $i++) {
            $setting = $nowpaymentsSettings[$i];
            echo "<p>- {$setting['key']}: " . substr($setting['value'], 0, 50) . "</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Пустой массив!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Setting::getStats()</h3>";
try {
    $stats = Setting::getStats();
    echo "<p>Статистика:</p>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Имитируем то, что делает контроллер пользователей
echo "<h2>👥 Тест контроллера пользователей</h2>";

echo "<h3>1. User::getUsers()</h3>";
try {
    $usersData = User::getUsers();
    echo "<p>Количество пользователей: " . count($usersData['users']) . "</p>";
    echo "<p>Всего в пагинации: " . $usersData['pagination']['total'] . "</p>";
    
    if (count($usersData['users']) > 0) {
        echo "<h4>Первые 3 пользователя:</h4>";
        for ($i = 0; $i < min(3, count($usersData['users'])); $i++) {
            $user = $usersData['users'][$i];
            echo "<p>- {$user['telegram_id']}: {$user['first_name']} {$user['last_name']}</p>";
        }
    } else {
        echo "<p style='color: red'>❌ Пустой массив пользователей!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h3>2. User::getStats()</h3>";
try {
    $userStats = User::getStats();
    echo "<p>Статистика пользователей:</p>";
    echo "<pre>" . print_r($userStats, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

echo "<h2>🎯 Выводы</h2>";
echo "<p>Если здесь данные есть, а в админке нет - проблема в представлениях.</p>";
echo "<p>Если здесь данных нет - проблема в моделях.</p>";

echo "<hr>";
echo "<p><a href='/admin/?page=settings'>🔗 Настройки в админке</a></p>";
echo "<p><a href='/admin/?page=users'>🔗 Пользователи в админке</a></p>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
