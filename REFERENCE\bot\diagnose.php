<?php
/**
 * diagnose.php
 * Диагностика проблем с Telegram Bot API
 */

require_once __DIR__ . '/config.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Диагностика Telegram Bot</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";
echo "</head><body>\n";

echo "<h1>🔧 Диагностика Telegram Bot</h1>\n";

// 1. Проверка конфигурации
echo "<h2>📋 1. Проверка конфигурации</h2>\n";
echo "<p><strong>Bot Token:</strong> " . (defined('BOT_TOKEN') ? substr(BOT_TOKEN, 0, 10) . "..." : "❌ НЕ ОПРЕДЕЛЕН") . "</p>\n";
echo "<p><strong>Webhook URL:</strong> " . (defined('WEBHOOK_URL') ? WEBHOOK_URL : "❌ НЕ ОПРЕДЕЛЕН") . "</p>\n";
echo "<p><strong>Web App URL:</strong> " . (defined('WEBAPP_URL') ? WEBAPP_URL : "❌ НЕ ОПРЕДЕЛЕН") . "</p>\n";
echo "<p><strong>Bot Username:</strong> " . (defined('BOT_USERNAME') ? BOT_USERNAME : "❌ НЕ ОПРЕДЕЛЕН") . "</p>\n";

// 2. Тест подключения к Telegram API
echo "<h2>🌐 2. Тест подключения к Telegram API</h2>\n";

// Улучшенная функция для запросов с детальной диагностикой
function diagnosticTelegramRequest($method, $data = []) {
    $url = TELEGRAM_API_URL . $method;
    
    echo "<h3>🔍 Тестируем метод: {$method}</h3>\n";
    echo "<p><strong>URL:</strong> <code>{$url}</code></p>\n";
    
    // Проверяем доступность через cURL с подробной информацией
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'UniQPaid Bot Diagnostic Tool');
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    // Захватываем verbose информацию
    $verboseFile = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verboseFile);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    
    // Получаем verbose лог
    rewind($verboseFile);
    $verboseLog = stream_get_contents($verboseFile);
    fclose($verboseFile);
    
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> ";
    if ($httpCode == 200) {
        echo "<span class='success'>{$httpCode} ✅</span></p>\n";
    } else {
        echo "<span class='error'>{$httpCode} ❌</span></p>\n";
    }
    
    if ($error) {
        echo "<p><strong>cURL Error:</strong> <span class='error'>{$error}</span></p>\n";
    }
    
    echo "<p><strong>Connection Time:</strong> " . round($info['connect_time'], 3) . "s</p>\n";
    echo "<p><strong>Total Time:</strong> " . round($info['total_time'], 3) . "s</p>\n";
    
    if ($result) {
        $response = json_decode($result, true);
        if ($response) {
            echo "<p><strong>Response:</strong> ";
            if ($response['ok']) {
                echo "<span class='success'>✅ OK</span></p>\n";
                echo "<details><summary>Показать ответ</summary><pre>" . htmlspecialchars(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre></details>\n";
                return $response['result'];
            } else {
                echo "<span class='error'>❌ ERROR</span></p>\n";
                echo "<p><strong>Error Description:</strong> <span class='error'>" . htmlspecialchars($response['description']) . "</span></p>\n";
                if (isset($response['error_code'])) {
                    echo "<p><strong>Error Code:</strong> {$response['error_code']}</p>\n";
                }
            }
        } else {
            echo "<p><strong>Response:</strong> <span class='error'>❌ Неверный JSON</span></p>\n";
            echo "<pre>" . htmlspecialchars(substr($result, 0, 500)) . "</pre>\n";
        }
    } else {
        echo "<p><strong>Response:</strong> <span class='error'>❌ Пустой ответ</span></p>\n";
    }
    
    // Показываем verbose лог при ошибках
    if ($httpCode != 200 || $error) {
        echo "<details><summary>Показать подробный лог cURL</summary><pre>" . htmlspecialchars($verboseLog) . "</pre></details>\n";
    }
    
    return false;
}

// Тест getMe
$botInfo = diagnosticTelegramRequest('getMe');

// Тест getWebhookInfo
$webhookInfo = diagnosticTelegramRequest('getWebhookInfo');

// 3. Проверка webhook
echo "<h2>🔗 3. Анализ Webhook</h2>\n";
if ($webhookInfo) {
    if (!empty($webhookInfo['url'])) {
        echo "<p class='success'>✅ Webhook установлен</p>\n";
        echo "<p><strong>URL:</strong> " . htmlspecialchars($webhookInfo['url']) . "</p>\n";
        echo "<p><strong>Pending Updates:</strong> " . ($webhookInfo['pending_update_count'] ?? 0) . "</p>\n";
        
        if (isset($webhookInfo['last_error_date'])) {
            echo "<p class='error'><strong>Последняя ошибка:</strong> " . date('Y-m-d H:i:s', $webhookInfo['last_error_date']) . "</p>\n";
            echo "<p class='error'><strong>Сообщение ошибки:</strong> " . htmlspecialchars($webhookInfo['last_error_message']) . "</p>\n";
        }
        
        if (isset($webhookInfo['ip_address'])) {
            echo "<p><strong>IP Address:</strong> " . $webhookInfo['ip_address'] . "</p>\n";
        }
    } else {
        echo "<p class='warning'>⚠️ Webhook не установлен</p>\n";
    }
} else {
    echo "<p class='error'>❌ Не удалось получить информацию о webhook</p>\n";
}

// 4. Проверка доступности webhook URL
echo "<h2>🌍 4. Проверка доступности Webhook URL</h2>\n";
if (defined('WEBHOOK_URL')) {
    echo "<p>Проверяем доступность: <code>" . WEBHOOK_URL . "</code></p>\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, WEBHOOK_URL);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Для тестирования
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode == 200) {
        echo "<p class='success'>✅ Webhook URL доступен (HTTP {$httpCode})</p>\n";
    } else {
        echo "<p class='error'>❌ Webhook URL недоступен (HTTP {$httpCode})</p>\n";
        if ($error) {
            echo "<p class='error'>Error: {$error}</p>\n";
        }
    }
}

// 5. Проверка файлов
echo "<h2>📁 5. Проверка файлов</h2>\n";
$files = ['config.php', 'webhook.php', 'set_webhook.php'];
foreach ($files as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "<p class='success'>✅ {$file} существует (" . filesize($path) . " байт)</p>\n";
    } else {
        echo "<p class='error'>❌ {$file} не найден</p>\n";
    }
}

// 6. Проверка логов
echo "<h2>📋 6. Последние логи</h2>\n";
if (file_exists(LOG_FILE)) {
    $logs = file_get_contents(LOG_FILE);
    $lastLogs = array_slice(explode("\n", $logs), -10);
    echo "<pre>" . htmlspecialchars(implode("\n", $lastLogs)) . "</pre>\n";
} else {
    echo "<p class='warning'>⚠️ Файл логов не найден</p>\n";
}

// 7. Рекомендации
echo "<h2>💡 7. Рекомендации</h2>\n";
echo "<ul>\n";

if (!$botInfo) {
    echo "<li class='error'>❌ Проверьте правильность BOT_TOKEN</li>\n";
    echo "<li class='info'>💡 Получите новый токен у @BotFather</li>\n";
}

if (!$webhookInfo || empty($webhookInfo['url'])) {
    echo "<li class='warning'>⚠️ Установите webhook: <a href='set_webhook.php'>set_webhook.php</a></li>\n";
}

if (isset($webhookInfo['last_error_date'])) {
    echo "<li class='error'>❌ Исправьте ошибки webhook</li>\n";
}

echo "<li class='info'>🔄 Попробуйте переустановить webhook</li>\n";
echo "<li class='info'>🧪 Протестируйте бота командой /start</li>\n";
echo "</ul>\n";

// 8. Быстрые действия
echo "<h2>⚡ 8. Быстрые действия</h2>\n";
echo "<p><a href='set_webhook.php'>🔗 Установить webhook</a></p>\n";
echo "<p><a href='?action=delete_webhook'>🗑️ Удалить webhook</a></p>\n";
echo "<p><a href='?action=test_bot'>🧪 Тест бота</a></p>\n";

// Обработка действий
if (isset($_GET['action'])) {
    echo "<h3>🔧 Выполнение действия: " . htmlspecialchars($_GET['action']) . "</h3>\n";
    
    switch ($_GET['action']) {
        case 'delete_webhook':
            $result = diagnosticTelegramRequest('deleteWebhook');
            if ($result) {
                echo "<p class='success'>✅ Webhook удален</p>\n";
            }
            break;
            
        case 'test_bot':
            $testChatId = $_GET['chat_id'] ?? null;
            if ($testChatId) {
                $result = diagnosticTelegramRequest('sendMessage', [
                    'chat_id' => $testChatId,
                    'text' => '🧪 Тест бота: ' . date('Y-m-d H:i:s')
                ]);
                if ($result) {
                    echo "<p class='success'>✅ Тестовое сообщение отправлено</p>\n";
                }
            } else {
                echo "<p class='warning'>⚠️ Укажите chat_id: ?action=test_bot&chat_id=YOUR_ID</p>\n";
            }
            break;
    }
}

echo "</body></html>\n";
?>
