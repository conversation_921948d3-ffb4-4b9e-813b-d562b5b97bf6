// === audio-manager.js ===
// Файл: js/audio-manager.js
// Описание: Управляет звуковыми эффектами.

class AudioManager {
  constructor() {
    this.audioContext = null;
    this.coinSoundBuffer = null;
    this.isInitialized = false;
    this.soundEnabled = true;
    this.fallbackGenerator = null;

    // Список возможных файлов звука монет (из оригинала)
    this.coinSoundFiles = [
      'audio/zvuk-monety.mp3',    // Приоритет 1 - твой файл!
      'audio/coins-drop.mp3',     // Приоритет 2
      'audio/coins-drop.wav',     // Приоритет 3
      'audio/coins-drop.ogg',     // Приоритет 4
      'audio/coins.mp3',          // Приоритет 5
      'audio/reward.mp3'          // Приоритет 6
    ];
  }

  init() {
    if (this.isInitialized) {
      console.log('[AudioManager] Уже инициализирован.');
      return;
    }
    console.log('[AudioManager] Инициализация...');
    this._loadAudio();
    this.setupFallbackGenerator();
    this.isInitialized = true;
  }

  async _loadAudio() {
    try {
      // Инициализируем аудио контекст
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      console.log('[AudioManager] Аудио контекст создан');

      // Пробуем загрузить звук монет из списка файлов
      let soundLoaded = false;
      for (const soundFile of this.coinSoundFiles) {
        try {
          console.log(`[AudioManager] Попытка загрузки: ${soundFile}`);
          const response = await fetch(soundFile);
          if (response.ok) {
            const arrayBuffer = await response.arrayBuffer();
            this.coinSoundBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            console.log(`[AudioManager] 🎵 Звук монет загружен из: ${soundFile}`);
            soundLoaded = true;
            break;
          }
        } catch (fileError) {
          console.log(`[AudioManager] Файл ${soundFile} не найден, пробуем следующий...`);
        }
      }

      if (!soundLoaded) {
        console.warn('[AudioManager] ⚠️ Ни один звуковой файл не найден, будет использован генератор');
      }

    } catch (e) {
      console.warn('[AudioManager] Не удалось инициализировать Web Audio API:', e);
    }
  }

  /**
   * Настройка генератора звука как запасного варианта (из оригинала)
   */
  setupFallbackGenerator() {
    if (!this.audioContext) return;

    this.fallbackGenerator = {
      generateCoinsSound: (amount = 10) => {
        if (!this.audioContext) return;

        try {
          // Количество монет зависит от суммы reward'а (из оригинала)
          const numCoins = Math.min(Math.max(3, Math.floor(amount / 2)), 8);

          // Создаем реалистичные звуки падающих монет
          const coinSounds = [];

          for (let i = 0; i < numCoins; i++) {
            coinSounds.push({
              freq: 800 + Math.random() * 600, // Случайная частота 800-1400Hz
              delay: i * 0.08 + Math.random() * 0.05, // Небольшие случайные задержки
              duration: 0.15 + Math.random() * 0.1, // Случайная длительность
              volume: 0.3 + Math.random() * 0.2 // Случайная громкость
            });
          }

          // Воспроизводим каждую монетку
          coinSounds.forEach(coin => {
            setTimeout(() => {
              this.createRealisticCoinSound(coin.freq, coin.duration, coin.volume);
            }, coin.delay * 1000);
          });

          // Добавляем финальный "звон" для больших наград
          if (amount >= 10) {
            setTimeout(() => {
              this.createBellSound();
            }, (numCoins * 0.08 + 0.2) * 1000);
          }

        } catch (error) {
          console.warn('[AudioManager] Ошибка генерации звука:', error);
        }
      }
    };
  }

  /**
   * Создает реалистичный звук монеты (из оригинала)
   */
  createRealisticCoinSound(frequency, duration, volume) {
    if (!this.audioContext) return;

    const now = this.audioContext.currentTime;

    // Создаем основной тон (металлический звук)
    const oscillator1 = this.audioContext.createOscillator();
    const oscillator2 = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();
    const filter = this.audioContext.createBiquadFilter();

    // Основная частота
    oscillator1.type = 'triangle';
    oscillator1.frequency.setValueAtTime(frequency, now);
    oscillator1.frequency.exponentialRampToValueAtTime(frequency * 0.7, now + duration);

    // Гармоника для металлического звука
    oscillator2.type = 'sawtooth';
    oscillator2.frequency.setValueAtTime(frequency * 2.1, now);
    oscillator2.frequency.exponentialRampToValueAtTime(frequency * 1.4, now + duration);

    // Фильтр для металлического оттенка
    filter.type = 'bandpass';
    filter.frequency.setValueAtTime(frequency * 1.5, now);
    filter.Q.setValueAtTime(2, now);

    // Огибающая громкости (быстрая атака, медленное затухание)
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(volume, now + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration);

    // Соединяем цепочку
    oscillator1.connect(filter);
    oscillator2.connect(gainNode);
    filter.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    // Запускаем
    oscillator1.start(now);
    oscillator2.start(now);
    oscillator1.stop(now + duration);
    oscillator2.stop(now + duration);
  }

  /**
   * Создает звук колокольчика для больших наград (из оригинала)
   */
  createBellSound() {
    if (!this.audioContext) return;

    const now = this.audioContext.currentTime;
    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    // Звон колокольчика
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(1200, now);
    oscillator.frequency.exponentialRampToValueAtTime(800, now + 0.5);

    gainNode.gain.setValueAtTime(0.2, now);
    gainNode.gain.exponentialRampToValueAtTime(0.001, now + 0.5);

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.start(now);
    oscillator.stop(now + 0.5);
  }

  /**
   * Воспроизводит звук монет (полная логика из оригинала)
   */
  playCoinsSound(amount = 10) {
    if (!this.soundEnabled) {
      console.log('[AudioManager] Звук отключен');
      return;
    }

    console.log(`[AudioManager] 🎵 Воспроизводим звук для ${amount} монет`);

    // Сначала пробуем воспроизвести загруженный звук
    if (this.coinSoundBuffer && this.audioContext) {
      try {
        const coinCount = Math.min(Math.max(1, Math.floor(amount / 5)), 5);
        for (let i = 0; i < coinCount; i++) {
          setTimeout(() => {
            try {
              const source = this.audioContext.createBufferSource();
              source.buffer = this.coinSoundBuffer;
              source.playbackRate.value = 1.0 + (Math.random() - 0.5) * 0.4;
              source.connect(this.audioContext.destination);
              source.start(0);
            } catch (e) {
              console.error('[AudioManager] Ошибка воспроизведения звука:', e);
            }
          }, i * 60);
        }
        return;
      } catch (error) {
        console.warn('[AudioManager] Ошибка воспроизведения файла, используем генератор:', error);
      }
    }

    // Если файл не загружен, используем генератор
    if (this.fallbackGenerator) {
      console.log('[AudioManager] Используем генератор звука');
      this.fallbackGenerator.generateCoinsSound(amount);
    } else {
      console.warn('[AudioManager] Звук недоступен - ни файл, ни генератор');
    }
  }
}

window.audioManager = new AudioManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.playCoinsSound = function(amount = 10) {
  if (window.audioManager && window.audioManager.playCoinsSound) {
    window.audioManager.playCoinsSound(amount);
  } else {
    console.warn('[AudioManager] playCoinsSound не найден!');
  }
};

// Создаем объект coinsAudio для совместимости с другими модулями
window.coinsAudio = {
  playCoinsSound: window.playCoinsSound,
  sounds: {
    coins: window.audioManager.coinSoundBuffer ? true : false
  }
};

console.log('🎵 [AudioManager] Аудио менеджер загружен с полной интеграцией.');