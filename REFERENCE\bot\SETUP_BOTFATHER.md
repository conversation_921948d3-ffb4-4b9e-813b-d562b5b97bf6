# 🤖 Настройка бота через @BotFather

## 📋 Пошаговая инструкция

### 1. Откройте @BotFather в Telegram
Перейдите к боту: https://t.me/BotFather

### 2. Измените название бота
```
/setname
@uniqpaid_paid_bot
UniQPaid - Earn <PERSON>o
```

### 3. Измените описание бота
```
/setdescription
@uniqpaid_paid_bot
🎯 UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!

💰 10 монет за каждый просмотр
🔄 Вывод в BTC, ETH, USDT, TRX
👥 Реферальная программа 10%

🚀 Нажмите /start чтобы начать!
```

### 4. Установите короткое описание
```
/setabouttext
@uniqpaid_paid_bot
🎉 Присоединяйся к UniQPaid и получай монеты за просмотр рекламы! Моментальный вывод!
```

### 5. Загрузите аватар бота
```
/setuserpic
@uniqpaid_paid_bot
```
Затем отправьте файл: `images/bot_avatar.svg` (конвертированный в PNG 512x512)

### 6. Настройте команды бота
```
/setcommands
@uniqpaid_paid_bot
start - 🚀 Начать работу с ботом
balance - 💰 Показать баланс
help - ❓ Помощь и инструкции
```

### 7. Настройте меню бота
```
/setmenubutton
@uniqpaid_paid_bot
text: 🚀 Открыть приложение
url: https://app.uniqpaid.com/test2/
```

### 8. Включите inline режим (опционально)
```
/setinline
@uniqpaid_paid_bot
Поделиться UniQPaid
```

### 9. Настройте домен для Web App
```
/setdomain
@uniqpaid_paid_bot
app.uniqpaid.com
```

## 🎨 Рекомендации по дизайну

### Цветовая схема:
- **Основной**: #4f46e5 (индиго)
- **Акцент**: #fbbf24 (золотой)
- **Градиент**: от #4f46e5 до #ec4899

### Ключевые слова для описания:
- Криптовалюта
- Заработок
- Реклама
- Моментальные выплаты
- Реферальная программа
- Web3
- DeFi

## 📱 Результат

После настройки ваш бот будет выглядеть профессионально:
- ✅ Красивая иконка с монетами
- ✅ Понятное название "UniQPaid - Earn Crypto"
- ✅ Подробное описание функций
- ✅ Настроенные команды
- ✅ Кнопка запуска приложения

## 🔗 Полезные ссылки

- Бот: https://t.me/uniqpaid_paid_bot
- Приложение: https://app.uniqpaid.com/test2/
- BotFather: https://t.me/BotFather

## 📝 Примечания

1. **Аватар**: Конвертируйте SVG в PNG 512x512 пикселей
2. **Описание**: Максимум 512 символов
3. **Команды**: Максимум 100 команд
4. **Домен**: Должен совпадать с URL приложения
