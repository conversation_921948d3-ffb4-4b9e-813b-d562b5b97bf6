<?php
/**
 * check_webhook.php
 * Скрипт для проверки статуса webhook
 */

require_once __DIR__ . '/config.php';

echo "=== Проверка статуса Webhook ===\n\n";

// Получаем информацию о боте
echo "1. Информация о боте:\n";
$botInfo = telegramRequest('getMe');
if ($botInfo) {
    echo "✅ Бот активен\n";
    echo "Имя: " . $botInfo['first_name'] . "\n";
    echo "Username: @" . $botInfo['username'] . "\n";
    echo "ID: " . $botInfo['id'] . "\n\n";
} else {
    echo "❌ Бот недоступен\n\n";
    exit(1);
}

// Проверяем webhook
echo "2. Статус webhook:\n";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo) {
    $isActive = !empty($webhookInfo['url']);
    
    echo ($isActive ? "✅" : "❌") . " Webhook " . ($isActive ? "активен" : "не установлен") . "\n";
    
    if ($isActive) {
        echo "URL: " . $webhookInfo['url'] . "\n";
        echo "Ожидающие обновления: " . ($webhookInfo['pending_update_count'] ?: 0) . "\n";
        
        if ($webhookInfo['last_error_message']) {
            echo "⚠️ Последняя ошибка: " . $webhookInfo['last_error_message'] . "\n";
            echo "Дата ошибки: " . date('Y-m-d H:i:s', $webhookInfo['last_error_date']) . "\n";
        } else {
            echo "✅ Ошибок нет\n";
        }
        
        // Проверяем, совпадает ли URL
        if ($webhookInfo['url'] === WEBHOOK_URL) {
            echo "✅ URL webhook корректный\n";
        } else {
            echo "⚠️ URL webhook не совпадает с настройками\n";
            echo "Ожидаемый: " . WEBHOOK_URL . "\n";
            echo "Текущий: " . $webhookInfo['url'] . "\n";
        }
    }
} else {
    echo "❌ Не удалось получить информацию о webhook\n";
}

// Проверяем доступность webhook файла
echo "\n3. Проверка доступности webhook файла:\n";
$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Telegram Bot Webhook Check'
    ]
]);

$webhookResponse = @file_get_contents(WEBHOOK_URL, false, $context);
if ($webhookResponse !== false) {
    echo "✅ Webhook файл доступен\n";
    if (empty($webhookResponse)) {
        echo "✅ Webhook отвечает корректно (пустой ответ ожидаем)\n";
    } else {
        echo "⚠️ Webhook возвращает данные: " . substr($webhookResponse, 0, 100) . "...\n";
    }
} else {
    echo "❌ Webhook файл недоступен\n";
    echo "Проверьте URL: " . WEBHOOK_URL . "\n";
}

// Показываем последние записи лога
echo "\n4. Последние записи лога:\n";
if (file_exists(LOG_FILE)) {
    $logContent = file_get_contents(LOG_FILE);
    $lines = explode("\n", trim($logContent));
    $lastLines = array_slice($lines, -5, 5);
    
    if (empty($lastLines) || (count($lastLines) == 1 && empty($lastLines[0]))) {
        echo "Лог пуст\n";
    } else {
        foreach ($lastLines as $line) {
            if (trim($line)) {
                echo $line . "\n";
            }
        }
    }
} else {
    echo "Файл лога не найден\n";
}

// Инструкции
echo "\n5. Инструкции:\n";
if ($isActive ?? false) {
    echo "✅ Webhook активен. Можете тестировать бота:\n";
    echo "• Откройте: https://t.me/" . $botInfo['username'] . "\n";
    echo "• Отправьте команду /start\n";
    echo "• Проверьте работу кнопок\n";
} else {
    echo "❌ Webhook не активен. Для активации:\n";
    echo "• Запустите: php bot/setup_webhook.php\n";
    echo "• Или используйте команду curl:\n";
    echo "curl -X POST \"https://api.telegram.org/bot" . BOT_TOKEN . "/setWebhook\" \\\n";
    echo "     -d \"url=" . WEBHOOK_URL . "\" \\\n";
    echo "     -d \"allowed_updates=[\\\"message\\\",\\\"callback_query\\\"]\"\n";
}

echo "\n=== Проверка завершена ===\n";
?>
