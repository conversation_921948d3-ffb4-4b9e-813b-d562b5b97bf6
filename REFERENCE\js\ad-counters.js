/**
 * === ad-counters.js ===
 * Модуль для управления счетчиками показов рекламы
 */

class AdCountersManager {
  constructor() {
    // Используем централизованную конфигурацию из AdsConfig
    this.dailyLimits = {};
    this.adTypeMapping = {};

    // Инициализируем конфигурацию из AdsConfig
    this.initializeFromConfig();

    this.localization = null;
    this.isInitialized = false;

    // Загружаем актуальные лимиты из админки
    this.loadLimitsFromServer();
  }

  /**
   * Инициализация конфигурации из AdsConfig
   */
  initializeFromConfig() {
    if (window.AdsConfig) {
      // Получаем лимиты из централизованной конфигурации
      const dailyLimit = window.AdsConfig.LIMITS?.DAILY_LIMIT_PER_TYPE || 20;

      // Получаем типы рекламы и создаем маппинги
      Object.values(window.AdsConfig.AD_TYPES).forEach(adType => {
        this.dailyLimits[adType.id] = dailyLimit;
        this.adTypeMapping[adType.id] = adType.counterId;
      });

      console.log('[AdCounters] ✅ Конфигурация загружена из AdsConfig:', this.adTypeMapping);
    } else {
      // Fallback на старые значения
      this.dailyLimits = {
        'native_banner': 20,
        'rewarded_video': 20,
        'interstitial': 20
      };

      this.adTypeMapping = {
        'native_banner': 'native-banner-counter',
        'rewarded_video': 'rewarded-video-counter',
        'interstitial': 'interstitial-counter'
      };

      console.warn('[AdCounters] ⚠️ AdsConfig не найден, используем fallback конфигурацию');
    }
  }

  /**
   * Загружает актуальные лимиты из админки
   */
  async loadLimitsFromServer() {
    try {
      console.log('[AdCounters] 📥 Загружаем актуальные лимиты из админки...');
      const response = await fetch('api/get_ad_rewards.php');
      const data = await response.json();

      if (data.success && data.limits) {
        // Обновляем лимиты для каждого типа рекламы
        if (data.limits.native_banner !== undefined) {
          this.dailyLimits['native_banner'] = data.limits.native_banner;
        }
        if (data.limits.interstitial !== undefined) {
          this.dailyLimits['interstitial'] = data.limits.interstitial;
        }
        if (data.limits.rewarded_video !== undefined) {
          this.dailyLimits['rewarded_video'] = data.limits.rewarded_video;
        }

        console.log('[AdCounters] ✅ Лимиты обновлены из админки:', this.dailyLimits);

        // Обновляем отображение счетчиков с новыми лимитами
        this.updateAllCounters();

        return true;
      } else {
        console.warn('[AdCounters] ⚠️ Ошибка загрузки лимитов:', data.error);
        return false;
      }
    } catch (error) {
      console.error('[AdCounters] ❌ Ошибка запроса лимитов:', error);
      return false;
    }
  }

  /**
   * Инициализация менеджера счетчиков
   */
  async init() {
    if (this.isInitialized) {
      console.warn('[AdCounters] Уже инициализирован');
      return;
    }

    // Ждем загрузки локализации
    await this.waitForLocalization();

    // Очищаем старые данные счетчиков
    this.cleanupOldCounters();

    // Обновляем все счетчики
    this.updateAllCounters();

    this.isInitialized = true;
    console.log('[AdCounters] ✅ Инициализирован');
  }

  /**
   * Ожидание загрузки системы локализации
   */
  async waitForLocalization() {
    let attempts = 0;
    const maxAttempts = 50; // 5 секунд максимум

    while (attempts < maxAttempts) {
      if (window.appLocalization && window.appLocalization.isLoaded) {
        this.localization = window.appLocalization;
        console.log('[AdCounters] 🌐 Локализация найдена');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }

    console.warn('[AdCounters] ⚠️ Локализация не найдена, используем дефолтные тексты');
  }

  /**
   * Получает текущую дату в UTC формате YYYY-MM-DD
   */
  getCurrentUTCDate() {
    const now = new Date();
    const utcYear = now.getUTCFullYear();
    const utcMonth = String(now.getUTCMonth() + 1).padStart(2, '0');
    const utcDay = String(now.getUTCDate()).padStart(2, '0');
    return `${utcYear}-${utcMonth}-${utcDay}`;
  }

  /**
   * Получает ключ для localStorage для определенного типа рекламы
   */
  getAdCountKey(adType) {
    const todayUTC = this.getCurrentUTCDate();
    return `ad_count_${adType}_${todayUTC}`;
  }

  /**
   * Получает количество просмотренной рекламы определенного типа за сегодня
   */
  getTodayAdCount(adType) {
    const key = this.getAdCountKey(adType);
    const count = localStorage.getItem(key);
    return count ? parseInt(count, 10) : 0;
  }

  /**
   * Получает оставшееся количество показов для типа рекламы
   */
  getRemainingCount(adType) {
    const currentCount = this.getTodayAdCount(adType);
    const limit = this.dailyLimits[adType] || 20;
    return Math.max(0, limit - currentCount);
  }

  /**
   * Форматирует текст счетчика с учетом локализации
   */
  formatCounterText(count, language = 'ru') {
    // Простая и надежная система переводов
    if (language === 'en') {
      if (count === 0) return 'limit reached';
      if (count === 1) return '1 ad view left';
      return `${count} ad views left`;
    } else {
      // Русский язык с правильными склонениями
      if (count === 0) return 'лимит исчерпан';
      if (count === 1) return 'остался 1 показ';
      if (count >= 2 && count <= 4) return `осталось ${count} показа`;
      return `осталось ${count} показов`;
    }
  }

  /**
   * Обновляет счетчик для определенного типа рекламы
   */
  updateCounter(adType) {
    console.log(`[AdCounters] 🔄 Обновление счетчика для типа: ${adType}`);

    const counterId = this.adTypeMapping[adType];
    if (!counterId) {
      console.warn(`[AdCounters] Неизвестный тип рекламы: ${adType}`);
      return;
    }

    const counterElement = document.getElementById(counterId);
    if (!counterElement) {
      console.warn(`[AdCounters] Элемент счетчика не найден: ${counterId}`);
      return;
    }

    const currentCount = this.getTodayAdCount(adType);
    const remainingCount = this.getRemainingCount(adType);

    // ИСПРАВЛЕНИЕ: Определяем язык с учетом эмуляции Telegram
    let currentLanguage = 'ru'; // По умолчанию русский

    if (this.localization?.currentLanguage) {
      currentLanguage = this.localization.currentLanguage;
    } else if (window.appLocalization?.currentLanguage) {
      currentLanguage = window.appLocalization.currentLanguage;
    } else if (window.Telegram?.WebApp?.initDataUnsafe?.user?.language_code) {
      // Определяем язык по Telegram
      const tgLang = window.Telegram.WebApp.initDataUnsafe.user.language_code;
      currentLanguage = (tgLang === 'en' || tgLang.startsWith('en')) ? 'en' : 'ru';
    } else {
      // В эмуляции определяем по IP или браузеру
      const browserLang = navigator.language || navigator.userLanguage || 'ru';
      currentLanguage = browserLang.startsWith('en') ? 'en' : 'ru';
    }

    const counterText = this.formatCounterText(remainingCount, currentLanguage);

    console.log(`[AdCounters] 📊 ${adType}: текущий=${currentCount}, осталось=${remainingCount}, текст="${counterText}"`);

    // Анимированное обновление текста
    this.animateCounterUpdate(counterElement, counterText);

    // Добавляем класс для стилизации если лимит достигнут
    if (remainingCount === 0) {
      counterElement.classList.add('limit-reached');
    } else {
      counterElement.classList.remove('limit-reached');
    }

    console.log(`[AdCounters] ✅ Обновлен счетчик ${adType}: ${counterText}`);
  }

  /**
   * Обновляет все счетчики
   */
  updateAllCounters() {
    // Проверяем и очищаем старые данные при каждом обновлении
    this.cleanupOldCounters();

    Object.keys(this.adTypeMapping).forEach(adType => {
      this.updateCounter(adType);
    });
    console.log('[AdCounters] 🔄 Все счетчики обновлены');
  }

  /**
   * Немедленно обновляет все счетчики без ожидания локализации
   */
  updateAllCountersImmediate() {
    console.log('[AdCounters] ⚡ Немедленное обновление счетчиков');

    // Сначала инициализируем конфигурацию, если еще не сделали
    if (Object.keys(this.adTypeMapping).length === 0) {
      console.log('[AdCounters] 🔧 Инициализируем конфигурацию...');
      this.initializeFromConfig();
    }

    // Проверяем и очищаем старые данные
    this.cleanupOldCounters();

    Object.keys(this.adTypeMapping).forEach(adType => {
      this.updateCounterImmediate(adType);
    });
    console.log('[AdCounters] ⚡ Все счетчики немедленно обновлены');
  }

  /**
   * Немедленно обновляет счетчик без ожидания локализации
   */
  updateCounterImmediate(adType) {
    console.log(`[AdCounters] ⚡ Немедленное обновление счетчика для типа: ${adType}`);

    const counterId = this.adTypeMapping[adType];
    if (!counterId) {
      console.warn(`[AdCounters] Неизвестный тип рекламы: ${adType}. Доступные типы:`, Object.keys(this.adTypeMapping));
      return;
    }

    console.log(`[AdCounters] 🔍 Ищем элемент с ID: ${counterId}`);
    const counterElement = document.getElementById(counterId);
    if (!counterElement) {
      console.warn(`[AdCounters] Элемент счетчика не найден: ${counterId}. Проверяем DOM...`);
      // Дополнительная отладка - проверяем, есть ли элементы в DOM
      const allCounters = document.querySelectorAll('[id*="counter"]');
      console.log(`[AdCounters] 🔍 Найденные элементы с "counter" в ID:`, Array.from(allCounters).map(el => el.id));
      return;
    }

    const currentCount = this.getTodayAdCount(adType);
    const remainingCount = this.getRemainingCount(adType);

    // Используем простой текст без локализации для немедленного обновления
    const counterText = `осталось ${remainingCount} показов`;

    console.log(`[AdCounters] ⚡ ${adType}: текущий=${currentCount}, осталось=${remainingCount}, текст="${counterText}"`);

    // Анимированное обновление текста
    this.animateCounterUpdate(counterElement, counterText);

    // Добавляем класс для стилизации если лимит достигнут
    if (remainingCount === 0) {
      counterElement.classList.add('limit-reached');
    } else {
      counterElement.classList.remove('limit-reached');
    }

    console.log(`[AdCounters] ⚡ Немедленно обновлен счетчик ${adType}: ${counterText}`);
  }

  /**
   * Увеличивает счетчик после успешного просмотра рекламы
   */
  incrementCounter(adType) {
    console.log(`[AdCounters] 🔄 Вызван incrementCounter для типа: ${adType}`);

    const key = this.getAdCountKey(adType);
    const currentCount = this.getTodayAdCount(adType);
    const newCount = currentCount + 1;

    console.log(`[AdCounters] 📊 Ключ: ${key}, Текущий: ${currentCount}, Новый: ${newCount}`);

    localStorage.setItem(key, newCount.toString());

    // Проверяем, что данные сохранились
    const savedCount = localStorage.getItem(key);
    console.log(`[AdCounters] 💾 Сохранено в localStorage: ${savedCount}`);

    // Обновляем отображение
    this.updateCounter(adType);

    // Также обновляем старую систему лимитов, если она доступна
    if (window.adsManagerFull && window.adsManagerFull.updateLimitsDisplay) {
      console.log(`[AdCounters] 🔄 Обновляем старую систему лимитов`);
      window.adsManagerFull.updateLimitsDisplay();
    }

    console.log(`[AdCounters] 📊 Счетчик ${adType} увеличен: ${newCount}/${this.dailyLimits[adType]}`);

    return newCount;
  }

  /**
   * Проверяет, достигнут ли лимит для определенного типа рекламы
   */
  isLimitReached(adType) {
    const currentCount = this.getTodayAdCount(adType);
    const limit = this.dailyLimits[adType] || 20;
    return currentCount >= limit;
  }

  /**
   * Обновляет язык интерфейса
   */
  updateLanguage(language) {
    console.log(`[AdCounters] 🌐 Обновление языка на: ${language}`);
    this.updateAllCounters();
  }

  /**
   * Сброс всех счетчиков (для тестирования)
   */
  resetAllCounters() {
    Object.keys(this.dailyLimits).forEach(adType => {
      const key = this.getAdCountKey(adType);
      localStorage.removeItem(key);
    });
    this.updateAllCounters();
    console.log('[AdCounters] 🔄 Все счетчики сброшены');
  }

  /**
   * Очищает старые данные счетчиков (старше текущего дня UTC)
   */
  cleanupOldCounters() {
    const currentUTCDate = this.getCurrentUTCDate();
    const keysToRemove = [];

    // Проходим по всем ключам localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);

      // Ищем ключи счетчиков рекламы
      if (key && key.startsWith('ad_count_')) {
        // Извлекаем дату из ключа (формат: ad_count_TYPE_YYYY-MM-DD)
        const datePart = key.split('_').slice(-3).join('-'); // Последние 3 части после разделения по _

        // Если дата не совпадает с текущей UTC датой, помечаем для удаления
        if (datePart !== currentUTCDate) {
          keysToRemove.push(key);
        }
      }
    }

    // Удаляем старые ключи
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`[AdCounters] 🗑️ Удален старый счетчик: ${key}`);
    });

    if (keysToRemove.length > 0) {
      console.log(`[AdCounters] 🔄 Очищено ${keysToRemove.length} старых счетчиков`);
    } else {
      console.log(`[AdCounters] ✅ Старых счетчиков не найдено`);
    }
  }

  /**
   * Получает время до следующего дня UTC
   */
  getTimeUntilNextDayUTC() {
    const now = new Date();
    const nextDayUTC = new Date(now);
    nextDayUTC.setUTCDate(now.getUTCDate() + 1);
    nextDayUTC.setUTCHours(0, 0, 0, 0);

    const msUntilNextDay = nextDayUTC.getTime() - now.getTime();
    const hours = Math.floor(msUntilNextDay / (1000 * 60 * 60));
    const minutes = Math.floor((msUntilNextDay % (1000 * 60 * 60)) / (1000 * 60));

    return {
      milliseconds: msUntilNextDay,
      hours,
      minutes,
      nextResetTime: nextDayUTC.toISOString()
    };
  }

  /**
   * Получает информацию о всех лимитах
   */
  getAllLimitsInfo() {
    const info = {};
    const timeUntilReset = this.getTimeUntilNextDayUTC();

    Object.keys(this.dailyLimits).forEach(adType => {
      const current = this.getTodayAdCount(adType);
      const limit = this.dailyLimits[adType];
      const remaining = Math.max(0, limit - current);

      info[adType] = {
        current,
        limit,
        remaining,
        isLimitReached: remaining === 0
      };
    });

    // Добавляем информацию о времени до сброса
    info._resetInfo = {
      currentUTCDate: this.getCurrentUTCDate(),
      timeUntilReset: timeUntilReset,
      nextResetTime: timeUntilReset.nextResetTime
    };

    return info;
  }

  /**
   * Анимированное обновление текста счетчика
   */
  animateCounterUpdate(element, newText) {
    if (!element) return;

    // Если текст не изменился, не анимируем
    if (element.textContent === newText) return;

    // Добавляем класс для анимации
    element.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
    element.style.opacity = '0.6';
    element.style.transform = 'scale(0.95)';

    setTimeout(() => {
      element.textContent = newText;
      element.style.opacity = '1';
      element.style.transform = 'scale(1)';

      // Убираем стили через некоторое время
      setTimeout(() => {
        element.style.transition = '';
        element.style.transform = '';
      }, 200);
    }, 100);
  }
}

// Создаем глобальный экземпляр
window.adCountersManager = new AdCountersManager();

// Немедленно обновляем счетчики при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
  console.log('[AdCounters] 🚀 DOM загружен, немедленно обновляем счетчики');

  // Быстрое обновление счетчиков без ожидания локализации
  window.adCountersManager.updateAllCountersImmediate();

  // Полная инициализация с локализацией
  setTimeout(() => {
    window.adCountersManager.init();
  }, 50);
});

// Также обновляем при полной загрузке страницы
window.addEventListener('load', () => {
  console.log('[AdCounters] 🔄 Страница полностью загружена, повторное обновление счетчиков');
  if (window.adCountersManager.isInitialized) {
    window.adCountersManager.updateAllCounters();
  }
});

// Экспортируем для модульной системы
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdCountersManager;
}

// Создаем глобальный экземпляр
window.adCountersManager = new AdCountersManager();

// Функция для тестирования системы счетчиков
window.testAdCounters = function() {
  console.log('[AdCounters] 🧪 ТЕСТ СИСТЕМЫ СЧЕТЧИКОВ');

  if (!window.adCountersManager) {
    console.error('[AdCounters] ❌ adCountersManager не найден');
    return;
  }

  console.log('[AdCounters] 📊 Текущие маппинги:', window.adCountersManager.adTypeMapping);
  console.log('[AdCounters] 📊 Лимиты:', window.adCountersManager.dailyLimits);

  // Проверяем элементы в DOM
  Object.entries(window.adCountersManager.adTypeMapping).forEach(([adType, counterId]) => {
    const element = document.getElementById(counterId);
    console.log(`[AdCounters] 🔍 ${adType} -> ${counterId}:`, element ? '✅ найден' : '❌ не найден');
    if (element) {
      console.log(`[AdCounters] 📝 Текущий текст: "${element.textContent}"`);
    }
  });

  // Принудительно обновляем счетчики
  window.adCountersManager.updateAllCountersImmediate();
};

// Функция для симуляции просмотра рекламы (для тестирования)
window.simulateAdView = function(adType = 'native_banner') {
  console.log(`[AdCounters] 🎬 Симуляция просмотра рекламы: ${adType}`);

  if (!window.adCountersManager) {
    console.error('[AdCounters] ❌ adCountersManager не найден');
    return;
  }

  // Увеличиваем счетчик
  window.adCountersManager.incrementCounter(adType);

  console.log(`[AdCounters] ✅ Счетчик ${adType} увеличен`);
};

// Универсальная функция для тестирования всех систем счетчиков
window.testAllCounterSystems = function() {
  console.log('🧪 ===== ТЕСТ ВСЕХ СИСТЕМ СЧЕТЧИКОВ =====');

  // Тестируем локальную систему
  if (window.testAdCounters) {
    console.log('📊 Тестируем локальную систему:');
    window.testAdCounters();
  }

  // Тестируем серверную систему
  if (window.testServerAdCounters) {
    console.log('🖥️ Тестируем серверную систему:');
    window.testServerAdCounters();
  }

  console.log('🧪 ===== ТЕСТ ЗАВЕРШЕН =====');
};

console.log('[AdCounters] 📊 Модуль загружен');
console.log('[AdCounters] 🧪 Для тестирования выполните: testAdCounters()');
console.log('[AdCounters] 🎬 Для симуляции просмотра: simulateAdView("native_banner")');
console.log('[AdCounters] 🔧 Для тестирования всех систем: testAllCounterSystems()');
