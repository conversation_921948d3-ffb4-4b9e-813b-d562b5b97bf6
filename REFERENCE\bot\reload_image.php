<?php
/**
 * reload_image.php
 * Перезагрузка картинки для Telegram бота
 */

require_once __DIR__ . '/config.php';

// Функция для принудительной перезагрузки картинки
function reloadBotImage() {
    // URL новой картинки с параметром времени для обхода кэша
    $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?' . time();
    
    // Тестовый chat_id (можно передать через GET параметр)
    $testChatId = $_GET['chat_id'] ?? 123456789; // Замените на ваш chat_id для тестирования
    
    // Тестовое сообщение
    $testMessage = "🔄 <b>Тестирование новой картинки</b>\n\n";
    $testMessage .= "Проверяем обновленную версию приветственного баннера.";
    
    // Простая клавиатура для теста
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '✅ Картинка обновлена!',
                    'callback_data' => 'image_updated'
                ]
            ]
        ]
    ];
    
    botLog("INFO: Попытка загрузки новой картинки: {$logoUrl}");
    
    // Пробуем отправить новую картинку
    $result = sendPhoto($testChatId, $logoUrl, $testMessage, $keyboard);
    
    if ($result) {
        botLog("SUCCESS: Новая картинка успешно загружена!");
        return [
            'success' => true,
            'message' => 'Картинка успешно обновлена!',
            'url' => $logoUrl,
            'file_id' => $result['photo'][0]['file_id'] ?? null
        ];
    } else {
        botLog("ERROR: Не удалось загрузить новую картинку");
        return [
            'success' => false,
            'message' => 'Ошибка при загрузке картинки',
            'url' => $logoUrl
        ];
    }
}

// Функция для получения file_id уже загруженной картинки
function getImageFileId() {
    $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?' . time();
    $testChatId = $_GET['chat_id'] ?? 123456789; // Замените на ваш chat_id
    
    $result = sendPhoto($testChatId, $logoUrl, "🔍 Получение file_id картинки");
    
    if ($result && isset($result['photo'])) {
        $fileId = $result['photo'][count($result['photo']) - 1]['file_id']; // Берем самое большое разрешение
        botLog("INFO: Получен file_id картинки: {$fileId}");
        return $fileId;
    }
    
    return null;
}

// Функция для очистки кэша картинки
function clearImageCache() {
    // Отправляем запрос с новым timestamp для обхода кэша
    $logoUrl = 'https://app.uniqpaid.com/test2/images/bot_welcome_banner.png?' . time();
    
    // Проверяем доступность картинки
    $headers = @get_headers($logoUrl);
    $httpCode = substr($headers[0], 9, 3);
    
    if ($httpCode == '200') {
        botLog("INFO: Картинка доступна по URL: {$logoUrl}");
        return true;
    } else {
        botLog("ERROR: Картинка недоступна. HTTP код: {$httpCode}");
        return false;
    }
}

// Обработка запроса
$action = $_GET['action'] ?? 'reload';

switch ($action) {
    case 'reload':
        $result = reloadBotImage();
        break;
        
    case 'get_file_id':
        $fileId = getImageFileId();
        $result = [
            'success' => $fileId !== null,
            'file_id' => $fileId,
            'message' => $fileId ? 'File ID получен' : 'Не удалось получить file_id'
        ];
        break;
        
    case 'clear_cache':
        $cacheCleared = clearImageCache();
        $result = [
            'success' => $cacheCleared,
            'message' => $cacheCleared ? 'Кэш очищен' : 'Ошибка очистки кэша'
        ];
        break;
        
    case 'test':
        // Простой тест доступности картинки
        $logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png';
        $headers = @get_headers($logoUrl);
        $result = [
            'success' => $headers !== false,
            'message' => $headers ? 'Картинка доступна' : 'Картинка недоступна',
            'url' => $logoUrl,
            'headers' => $headers
        ];
        break;
        
    default:
        $result = [
            'success' => false,
            'message' => 'Неизвестное действие'
        ];
}

// Выводим результат
header('Content-Type: application/json; charset=utf-8');
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

// Также выводим HTML версию для удобства
if (!isset($_GET['format']) || $_GET['format'] !== 'json') {
    echo "\n\n<!-- HTML версия для удобства -->\n";
    echo "<!DOCTYPE html>\n<html>\n<head>\n";
    echo "<meta charset='utf-8'>\n";
    echo "<title>Перезагрузка картинки бота</title>\n";
    echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";
    echo "</head>\n<body>\n";
    echo "<h1>🤖 Перезагрузка картинки Telegram бота</h1>\n";
    
    if ($result['success']) {
        echo "<p class='success'>✅ " . htmlspecialchars($result['message']) . "</p>\n";
    } else {
        echo "<p class='error'>❌ " . htmlspecialchars($result['message']) . "</p>\n";
    }
    
    echo "<h3>Доступные действия:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='?action=reload'>🔄 Перезагрузить картинку</a></li>\n";
    echo "<li><a href='?action=get_file_id'>🆔 Получить file_id</a></li>\n";
    echo "<li><a href='?action=clear_cache'>🗑️ Очистить кэш</a></li>\n";
    echo "<li><a href='?action=test'>🧪 Тест доступности</a></li>\n";
    echo "</ul>\n";
    
    echo "<h3>Результат:</h3>\n";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
    
    echo "<p class='info'>💡 <strong>Инструкция:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Загрузите новую картинку <code>bot_welcome_banner.png</code> на сервер</li>\n";
    echo "<li>Нажмите 'Перезагрузить картинку' для обновления</li>\n";
    echo "<li>Протестируйте бота командой /start</li>\n";
    echo "</ol>\n";
    
    echo "</body>\n</html>\n";
}
?>
