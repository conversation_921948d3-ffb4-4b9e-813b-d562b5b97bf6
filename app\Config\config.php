<?php
declare(strict_types=1);

/**
 * Application configuration file
 * Contains all application settings and constants
 */

// Debug mode
define('DEBUG', true);

// Database configuration
define('DB_PATH', realpath(__DIR__ . '/../../database/app.sqlite'));

// Application paths
define('APP_ROOT', __DIR__ . '/../..');
define('APP_PATH', __DIR__ . '/..');
define('VIEW_PATH', APP_PATH . '/Views');
define('PUBLIC_PATH', APP_ROOT . '/public');

// Application settings
define('APP_NAME', 'UniQPaid Admin');
define('APP_VERSION', '1.0.0');

// Security settings
define('SESSION_LIFETIME', 3600); // 1 hour

// Telegram Bot settings (will be moved to database later)
define('TELEGRAM_BOT_TOKEN', '**********************************************');
define('BOT_USERNAME', 'uniqpaid_paid_bot');

// Error reporting
if (DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
} else {
    error_reporting(0);
    ini_set('display_errors', '0');
}

// Set timezone to UTC (ALWAYS USE UTC!)
date_default_timezone_set('UTC');

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Force DEV_MODE for SQLite issues
define('DEV_MODE', true);
