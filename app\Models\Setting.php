<?php
declare(strict_types=1);

namespace App\Models;

use App\Core\Database;

/**
 * Setting model for application settings
 */
class Setting
{
    /**
     * Get all settings by category
     *
     * @param string $category Category (bot or miniapp)
     * @return array Settings array
     */
    public static function getByCategory(string $category): array
    {
        return Database::query(
            "SELECT * FROM settings WHERE category = ? ORDER BY key ASC",
            [$category]
        );
    }
    
    /**
     * Get setting value by category and key
     *
     * @param string $category Category
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value
     */
    public static function getValue(string $category, string $key, $default = null)
    {
        $setting = Database::queryOne(
            "SELECT * FROM settings WHERE category = ? AND key = ?",
            [$category, $key]
        );
        
        if (!$setting) {
            return $default;
        }
        
        // Convert value based on type
        switch ($setting['type']) {
            case 'number':
                return is_numeric($setting['value']) ? (float) $setting['value'] : $default;
            case 'boolean':
                return filter_var($setting['value'], FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($setting['value'], true) ?: $default;
            default:
                return $setting['value'];
        }
    }
    
    /**
     * Set setting value
     *
     * @param string $category Category
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param string $type Value type
     * @param string $description Description
     * @return bool Success
     */
    public static function setValue(string $category, string $key, $value, string $type = 'string', string $description = ''): bool
    {
        // Convert value to string for storage
        $stringValue = $value;
        if ($type === 'json') {
            $stringValue = json_encode($value);
        } elseif ($type === 'boolean') {
            $stringValue = $value ? '1' : '0';
        } else {
            $stringValue = (string) $value;
        }
        
        // Check if setting exists
        $existing = Database::queryOne(
            "SELECT id FROM settings WHERE category = ? AND key = ?",
            [$category, $key]
        );



        if ($existing) {
            // Update existing
            $result = Database::execute(
                "UPDATE settings SET value = ?, type = ?, description = ?, updated_at = datetime('now', 'utc') WHERE category = ? AND key = ?",
                [$stringValue, $type, $description, $category, $key]
            );

        } else {
            // Insert new
            $result = Database::execute(
                "INSERT INTO settings (category, key, value, type, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                [$category, $key, $stringValue, $type, $description]
            );

        }
        
        return $result > 0;
    }
    
    /**
     * Get all settings grouped by category
     *
     * @return array Settings grouped by category
     */
    public static function getAll(): array
    {
        $settings = Database::query("SELECT * FROM settings ORDER BY category ASC, key ASC");
        
        $grouped = ['bot' => [], 'miniapp' => [], 'nowpayments' => []];
        foreach ($settings as $setting) {
            $category = $setting['category'] ?? 'miniapp';
            if (isset($grouped[$category])) {
                $grouped[$category][] = $setting;
            }
        }
        
        return $grouped;
    }
    
    /**
     * Delete setting
     *
     * @param string $category Category
     * @param string $key Setting key
     * @return bool Success
     */
    public static function delete(string $category, string $key): bool
    {
        $result = Database::execute(
            "DELETE FROM settings WHERE category = ? AND key = ?",
            [$category, $key]
        );
        
        return $result > 0;
    }
    
    /**
     * Get statistics about settings
     *
     * @return array Statistics
     */
    public static function getStats(): array
    {
        $stats = Database::query(
            "SELECT category, COUNT(*) as cnt FROM settings GROUP BY category"
        );

        $result = ['bot' => 0, 'miniapp' => 0, 'nowpayments' => 0, 'total' => 0];

        foreach ($stats as $stat) {
            $category = $stat['category'] ?? '';
            $count = (int) ($stat['cnt'] ?? 0);
            
            if (isset($result[$category])) {
                $result[$category] = $count;
                $result['total'] += $count;
            }
        }
        
        return $result;
    }

    /**
     * Get settings for display with proper formatting
     *
     * @param string $category Category
     * @return array Formatted settings
     */
    public static function getFormattedByCategory(string $category): array
    {
        $settings = self::getByCategory($category);
        $formatted = [];

        foreach ($settings as $setting) {
            $formatted[] = [
                'key' => $setting['key'],
                'value' => $setting['value'],
                'type' => $setting['type'],
                'description' => $setting['description'],
                'display_value' => self::formatValueForDisplay($setting['value'], $setting['type']),
                'updated_at' => $setting['updated_at'] ?? date('Y-m-d H:i:s')
            ];
        }

        return $formatted;
    }

    /**
     * Format value for display
     *
     * @param string $value Raw value
     * @param string $type Value type
     * @return string Formatted value
     */
    private static function formatValueForDisplay(string $value, string $type): string
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN) ? 'Да' : 'Нет';
            case 'number':
                return is_numeric($value) ? number_format((float) $value, 3) : $value;
            case 'json':
                $decoded = json_decode($value, true);
                return $decoded ? json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $value;
            default:
                return $value;
        }
    }

    /**
     * Update multiple settings at once
     *
     * @param string $category Category
     * @param array $settings Array of key => value pairs
     * @return bool Success
     */
    public static function updateMultiple(string $category, array $settings): bool
    {
        try {
            Database::beginTransaction();

            foreach ($settings as $key => $value) {
                self::setValue($category, $key, $value);
            }

            Database::commit();
            return true;
        } catch (\Exception $e) {
            Database::rollback();
            return false;
        }
    }
}
?>
