<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Очистка кэша Telegram</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            text-align: center;
        }
        
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
            display: inline-block;
            text-decoration: none;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔄</div>
        <h1>Очистка кэша Telegram WebApp</h1>
        <p>Эта страница поможет принудительно обновить кэш Telegram и загрузить новые версии файлов.</p>
        
        <div class="info">
            <h3>📋 Инструкция по очистке кэша:</h3>
            <ol>
                <li><strong>Закройте</strong> Telegram полностью</li>
                <li><strong>Очистите кэш</strong> приложения Telegram в настройках</li>
                <li><strong>Перезапустите</strong> Telegram</li>
                <li><strong>Откройте бота</strong> заново через /start</li>
                <li><strong>Нажмите кнопки</strong> ниже для тестирования</li>
            </ol>
        </div>
        
        <button class="button" onclick="testAPI()">🧪 Тест API</button>
        <button class="button" onclick="forceReload()">🔄 Принудительная перезагрузка</button>
        <button class="button" onclick="clearLocalStorage()">🗑️ Очистить localStorage</button>
        
        <div id="status" class="status"></div>
        
        <div class="info">
            <h3>🔧 Технические детали:</h3>
            <p><strong>Текущий timestamp:</strong> <span id="current-time"></span></p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Telegram WebApp:</strong> <span id="telegram-status"></span></p>
        </div>
        
        <div class="timestamp">
            Последнее обновление: <span id="last-update"></span>
        </div>
    </div>

    <script>
        // Обновляем информацию на странице
        function updateInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
            document.getElementById('telegram-status').textContent = window.Telegram ? 'Доступен' : 'Недоступен';
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Тест API с принудительным обходом кэша
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status';
            statusDiv.innerHTML = '⏳ Тестируем API...';
            
            try {
                // Добавляем случайный параметр для обхода кэша
                const timestamp = Date.now();
                const random = Math.random().toString(36).substring(7);
                
                const response = await fetch(`api/check-block-status.php?t=${timestamp}&r=${random}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    body: JSON.stringify({
                        initData: 'test_cache_clear_' + timestamp
                    })
                });
                
                const data = await response.json();
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    ✅ API отвечает!<br>
                    <strong>Статус:</strong> ${response.status}<br>
                    <strong>Ответ:</strong> ${JSON.stringify(data, null, 2)}
                `;
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Ошибка API: ${error.message}`;
            }
        }
        
        // Принудительная перезагрузка страницы
        function forceReload() {
            // Добавляем timestamp к URL для обхода кэша
            const timestamp = Date.now();
            const currentUrl = window.location.href.split('?')[0];
            window.location.href = `${currentUrl}?cache_bust=${timestamp}`;
        }
        
        // Очистка localStorage
        function clearLocalStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                const statusDiv = document.getElementById('status');
                statusDiv.style.display = 'block';
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ localStorage и sessionStorage очищены!';
                
                // Перезагружаем через 2 секунды
                setTimeout(() => {
                    forceReload();
                }, 2000);
                
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.style.display = 'block';
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Ошибка очистки: ${error.message}`;
            }
        }
        
        // Автоматическое обновление информации каждые 5 секунд
        setInterval(updateInfo, 5000);
        
        // Инициализация при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            updateInfo();
            
            // Автоматически тестируем API через 1 секунду
            setTimeout(testAPI, 1000);
        });
        
        // Обработка сообщений от Telegram WebApp
        if (window.Telegram && window.Telegram.WebApp) {
            window.Telegram.WebApp.ready();
            console.log('🔄 Telegram WebApp готов к очистке кэша');
        }
    </script>
</body>
</html>
