<?php
declare(strict_types=1);

try {
    $dbPath = 'd:\\OSPanel\\domains\\argun-supertest.loc\\database\\app.sqlite';
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "--- SETTINGS ---\n";
    $settingsQuery = $db->query('SELECT * FROM settings');
    $settings = $settingsQuery->fetchAll(PDO::FETCH_ASSOC);
    if (empty($settings)) {
        echo "Table 'settings' is empty.\n";
    } else {
        print_r($settings);
    }

    echo "\n--- ADMIN USERS ---\n";
    $usersQuery = $db->query('SELECT * FROM admin_users');
    $users = $usersQuery->fetchAll(PDO::FETCH_ASSOC);
    if (empty($users)) {
        echo "Table 'admin_users' is empty.\n";
    } else {
        print_r($users);
    }

} catch (Exception $e) {
    echo "An error occurred: " . $e->getMessage();
}
?>