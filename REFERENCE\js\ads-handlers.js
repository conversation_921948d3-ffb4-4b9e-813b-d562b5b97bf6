/**
 * === ads-handlers.js ===
 * ОТКЛЮЧЕНО: Обработчики кнопок рекламы перенесены в ads-manager-full.js
 * Этот файл отключен для предотвращения дублирования запросов
 */

console.log('[ads-handlers.js] ОТКЛЮЧЕН - используется ads-manager-full.js');

/**
 * ОТКЛЮЧЕНО: Обработчик баннерной рекламы (как в оригинале)
 */
function handleWatchAdClick_DISABLED() {
  // Проверка на блокировку кнопки или активный таймер
  if (isButtonPressed || (watchAdButton && watchAdButton.disabled)) {
    console.warn("[ADS-MANAGER] Кнопка заблокирована или идет таймер - игнорируем клик");
    return;
  }

  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    if (tg && tg.showAlert) {
      tg.showAlert("Модуль рекламы не загружен.");
    }
    return;
  }

  console.log("[ADS-MANAGER] Клик по кнопке баннерной рекламы");
  showStatus("Загрузка рекламы...", "info");

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[ADS-MANAGER] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[ADS-MANAGER] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  // Блокируем кнопку на время показа
  if (watchAdButton) watchAdButton.disabled = true;

  try {
    // Используем динамическую конфигурацию из ads-config.js
    console.log("[ADS-MANAGER] Запуск рекламы для первой кнопки...");

    // Получаем конфигурацию из ads-config.js
    const adConfig = window.AdsConfig?.AD_TYPES?.NATIVE_BANNER;
    if (adConfig && typeof adsController[adConfig.method] === 'function') {
      console.log(`[ADS-MANAGER] Вызов ${adConfig.method}(${adConfig.params}) из конфигурации`);

      // Вызываем метод из конфигурации
      adsController[adConfig.method](adConfig.params)
        .then((result) => {
          console.log("[ADS-MANAGER] Успешный показ рекламы (первая кнопка):", result);
          showStatus("Реклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            startCountdown(watchAdButton, 20);
          }
        })
        .catch((error) => {
          console.warn("[ADS-MANAGER] Ошибка автоперехода по баннеру-превью:", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(watchAdButton, 20);
          } else {
            // Пробуем альтернативный метод при ошибке
            if (typeof adsController.triggerInterstitialBanner === 'function') {
              console.log("[ADS-MANAGER] Пробуем альтернативный метод triggerInterstitialBanner(true)");

              adsController.triggerInterstitialBanner(true)
                .then((result) => {
                  console.log("[ADS-MANAGER] Успешный автопереход по баннеру (альтернативный метод):", result);
                  showStatus("Реклама просмотрена! Начисляем награду...", "info");
                  return recordAdView(AD_TYPES.NATIVE_BANNER);
                })
                .then((success) => {
                  if (success) {
                    startCountdown(watchAdButton, 20);
                  }
                })
                .catch((altError) => {
                  console.warn("[ADS-MANAGER] Ошибка альтернативного метода:", altError);
                  handleAdError(altError);
                  startCountdown(watchAdButton, 20);
                });
            } else {
              handleAdError(error);
              startCountdown(watchAdButton, 20);
            }
          }
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[ADS-MANAGER] Операция автоперехода по баннеру-превью завершена");
          isAdShowing = false;
        });
    }
    // Если triggerNativeNotification недоступен, пробуем triggerInterstitialBanner
    else if (typeof adsController.triggerInterstitialBanner === 'function') {
      console.log("[ADS-MANAGER] Метод triggerNativeNotification недоступен, пробуем triggerInterstitialBanner(true)");

      adsController.triggerInterstitialBanner(true)
        .then((result) => {
          console.log("[ADS-MANAGER] Успешный автопереход по баннеру:", result);
          showStatus("Реклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            startCountdown(watchAdButton, 20);
          }
        })
        .catch((error) => {
          console.warn("[ADS-MANAGER] Ошибка автоперехода по баннеру:", error);
          handleAdError(error);
          startCountdown(watchAdButton, 20);
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[ADS-MANAGER] Операция автоперехода по баннеру завершена");
          isAdShowing = false;
        });
    } else {
      // Если оба метода недоступны, выдаем ошибку
      throw new Error("Методы показа баннера-превью недоступны");
    }
  } catch (error) {
    console.error("[ADS-MANAGER] Критическая ошибка при вызове баннера-превью:", error);
    showStatus(`Ошибка показа баннера-превью: ${error.message}`, "error");
    if (tg && tg.showAlert) {
      tg.showAlert(`Ошибка показа баннера-превью: ${error.message}`);
    }
    if (watchAdButton) watchAdButton.disabled = false;
    isAdShowing = false;
    startCountdown(watchAdButton, 20);
  }
}

/**
 * ОТКЛЮЧЕНО: Обработчик видеорекламы (как в оригинале)
 */
function handleWatchVideoClick_DISABLED() {
  // Проверка на блокировку кнопки или активный таймер
  if (isButtonPressed || (watchVideoButton && watchVideoButton.disabled)) {
    console.warn("[ADS-MANAGER] Кнопка видео заблокирована или идет таймер - игнорируем клик");
    return;
  }

  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    if (tg && tg.showAlert) {
      tg.showAlert("Модуль рекламы не загружен.");
    }
    return;
  }

  console.log("[ADS-MANAGER] Клик по кнопке видеорекламы");
  showStatus("Загрузка видеорекламы...", "info");

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[ADS-MANAGER] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[ADS-MANAGER] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  // Блокируем кнопку на время показа
  if (watchVideoButton) watchVideoButton.disabled = true;

  try {
    // Используем динамическую конфигурацию из ads-config.js
    console.log("[ADS-MANAGER] Запуск видеорекламы...");

    // Получаем конфигурацию из ads-config.js
    const adConfig = window.AdsConfig?.AD_TYPES?.REWARDED_VIDEO;
    if (adConfig && typeof adsController[adConfig.method] === 'function') {
      console.log(`[ADS-MANAGER] Вызов ${adConfig.method}(${adConfig.params}) из конфигурации`);

      // Вызываем метод из конфигурации
      adsController[adConfig.method](adConfig.params)
        .then((result) => {
          console.log("[ADS-MANAGER] Успешный показ видеорекламы:", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.REWARDED_VIDEO);
        })
        .then((success) => {
          if (success) {
            startCountdown(watchVideoButton, 20);
          }
        })
        .catch((error) => {
          console.warn("[ADS-MANAGER] Ошибка показа видеорекламы:", error);
          handleVideoError(error);
          startCountdown(watchVideoButton, 20);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[ADS-MANAGER] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    }
    // Если triggerInterstitialVideo недоступен, пробуем showRewardedVideo
    else if (typeof adsController.showRewardedVideo === 'function') {
      console.log("[ADS-MANAGER] Метод triggerInterstitialVideo недоступен, пробуем showRewardedVideo");

      // Используем специальный метод для показа видеорекламы
      adsController.showRewardedVideo()
        .then((result) => {
          console.log("[ADS-MANAGER] Успешный показ видеорекламы (альтернативный метод):", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.REWARDED_VIDEO);
        })
        .then((success) => {
          if (success) {
            startCountdown(watchVideoButton, 20);
          }
        })
        .catch((error) => {
          console.warn("[ADS-MANAGER] Ошибка показа видеорекламы (альтернативный метод):", error);
          handleVideoError(error);
          startCountdown(watchVideoButton, 20);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[ADS-MANAGER] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    } else {
      // Если все методы недоступны, выдаем ошибку
      throw new Error("Методы показа видеорекламы недоступны");
    }
  } catch (error) {
    console.error("[ADS-MANAGER] Критическая ошибка при вызове видеорекламы:", error);
    showStatus(`Ошибка показа видеорекламы: ${error.message}`, "error");
    if (tg && tg.showAlert) {
      tg.showAlert(`Ошибка показа видеорекламы: ${error.message}`);
    }
    if (watchVideoButton) watchVideoButton.disabled = false;
    isAdShowing = false;
    startCountdown(watchVideoButton, 20);
  }
}

/**
 * Функция для обработки ошибок рекламы
 */
function handleAdError(error) {
  console.warn("[ADS-MANAGER] Ошибка показа баннера-превью:", error);
  let userFriendlyMessage = "В данный момент баннер-превью недоступен";

  if (error instanceof Error) {
    const reason = error.message;
    if (reason.toLowerCase().includes('no ad') || reason.toLowerCase().includes('no ads')) {
      userFriendlyMessage = "В данный момент реклама недоступна";
    } else if (reason.toLowerCase().includes('network') || reason.toLowerCase().includes('connection')) {
      userFriendlyMessage = "Проблемы с подключением к интернету";
    } else if (reason.toLowerCase().includes('timeout')) {
      userFriendlyMessage = "Превышено время ожидания загрузки рекламы";
    }
  }

  // Показываем пользователю понятное сообщение
  showStatus(userFriendlyMessage, "error");
  if (tg && tg.HapticFeedback) {
    tg.HapticFeedback.notificationOccurred("warning");
  }
}

/**
 * Функция для обработки ошибок видеорекламы
 */
function handleVideoError(error) {
  console.warn("[ADS-MANAGER] Ошибка показа видеорекламы:", error);
  let userFriendlyMessage = "В данный момент видеореклама недоступна";

  if (error instanceof Error) {
    const reason = error.message;
    if (reason.toLowerCase().includes('no ad') || reason.toLowerCase().includes('no ads')) {
      userFriendlyMessage = "В данный момент видеореклама недоступна";
    } else if (reason.toLowerCase().includes('network') || reason.toLowerCase().includes('connection')) {
      userFriendlyMessage = "Проблемы с подключением к интернету";
    } else if (reason.toLowerCase().includes('timeout')) {
      userFriendlyMessage = "Превышено время ожидания загрузки видеорекламы";
    }
  }

  // Показываем пользователю понятное сообщение
  showStatus(userFriendlyMessage, "error");
  if (tg && tg.HapticFeedback) {
    tg.HapticFeedback.notificationOccurred("warning");
  }
}
