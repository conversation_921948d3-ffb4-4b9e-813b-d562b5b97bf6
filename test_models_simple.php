<?php
// Простой тест моделей
session_start();

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;
use App\Models\Setting;

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧪 Простой тест моделей</h1>";

Database::init();

// Тест 1: Прямой SQL запрос
echo "<h2>1️⃣ Прямой SQL</h2>";
try {
    $directSettings = Database::query("SELECT * FROM settings LIMIT 5");
    echo "<p>Прямой SQL вернул: " . count($directSettings) . " записей</p>";
    
    if (count($directSettings) > 0) {
        echo "<h3>Первая запись:</h3>";
        echo "<pre>" . print_r($directSettings[0], true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 2: Setting::getByCategory
echo "<h2>2️⃣ Setting::getByCategory('miniapp')</h2>";
try {
    $miniappSettings = Setting::getByCategory('miniapp');
    echo "<p>getByCategory('miniapp') вернул: " . count($miniappSettings) . " записей</p>";
    
    if (count($miniappSettings) > 0) {
        echo "<h3>Первая запись:</h3>";
        echo "<pre>" . print_r($miniappSettings[0], true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 3: Setting::getFormattedByCategory
echo "<h2>3️⃣ Setting::getFormattedByCategory('miniapp')</h2>";
try {
    $formattedSettings = Setting::getFormattedByCategory('miniapp');
    echo "<p>getFormattedByCategory('miniapp') вернул: " . count($formattedSettings) . " записей</p>";
    
    if (count($formattedSettings) > 0) {
        echo "<h3>Первая запись:</h3>";
        echo "<pre>" . print_r($formattedSettings[0], true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Ошибка: " . $e->getMessage() . "</p>";
}

// Тест 4: Проверяем все категории
echo "<h2>4️⃣ Все категории</h2>";
$categories = ['bot', 'miniapp', 'nowpayments'];
foreach ($categories as $category) {
    try {
        $count = Database::query("SELECT COUNT(*) as cnt FROM settings WHERE category = ?", [$category])[0]['cnt'];
        echo "<p>$category: $count настроек</p>";
    } catch (Exception $e) {
        echo "<p style='color: red'>❌ Ошибка $category: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
