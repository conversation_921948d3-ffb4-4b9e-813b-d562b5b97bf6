<?php
/**
 * update_golden_banner.php
 * Обновление супер стильного золотистого баннера бота
 */

require_once __DIR__ . '/config.php';

// Получаем chat_id из параметра URL
$chatId = $_GET['chat_id'] ?? null;

if (!$chatId) {
    echo "❌ Ошибка: Не указан chat_id\n";
    echo "Использование: update_golden_banner.php?chat_id=YOUR_CHAT_ID\n";
    exit;
}

// URL нового золотистого баннера с timestamp для обхода кэша
$logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_golden_banner.png?' . time();

// Сообщение для теста
$message = "🌟 <b>НОВЫЙ ЗОЛОТИСТЫЙ БАННЕР!</b>\n\n";
$message .= "✨ Супер стильный премиальный дизайн\n";
$message .= "🏆 Золотистые градиенты без размытий\n";
$message .= "💎 Актуальные криптовалюты: TON, USDT, BTC, ETH\n";
$message .= "🚀 Файл: bot_welcome_golden_banner.png\n";
$message .= "⏰ Время обновления: " . date('Y-m-d H:i:s');

// Клавиатура
$keyboard = [
    'inline_keyboard' => [
        [
            [
                'text' => '🌟 Золотистый баннер обновлен!',
                'callback_data' => 'golden_banner_updated'
            ]
        ],
        [
            [
                'text' => '🚀 Запустить приложение',
                'web_app' => ['url' => WEBAPP_URL]
            ]
        ],
        [
            [
                'text' => '💰 Мой баланс',
                'callback_data' => 'my_balance'
            ],
            [
                'text' => '👥 Друзья',
                'callback_data' => 'invite_friends'
            ]
        ]
    ]
];

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Обновление золотистого баннера</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#1a1a1a;color:#fff;} 
.success{color:#FFD700;background:#2a2a2a;padding:15px;border-radius:8px;border:2px solid #FFD700;} 
.error{color:#ff4444;background:#2a2a2a;padding:15px;border-radius:8px;border:2px solid #ff4444;} 
.info{color:#00ffff;} 
.banner{background:linear-gradient(135deg,#FFD700,#FFC107);color:#1a1a1a;padding:20px;border-radius:12px;margin:20px 0;text-align:center;font-weight:bold;}
code{background:#333;padding:2px 6px;border-radius:4px;color:#FFD700;}
a{color:#FFD700;}
</style>";
echo "</head><body>\n";

echo "<div class='banner'>\n";
echo "<h1>🌟 Обновление золотистого баннера Telegram бота</h1>\n";
echo "</div>\n";

echo "<p>📤 Отправляем новый золотистый баннер...</p>\n";
echo "<p><strong>URL:</strong> <code>{$logoUrl}</code></p>\n";
echo "<p><strong>Chat ID:</strong> <code>{$chatId}</code></p>\n";

// Логируем попытку
botLog("INFO: Попытка обновления золотистого баннера для chat_id: {$chatId}");
botLog("INFO: URL картинки: {$logoUrl}");

// Отправляем картинку
$result = sendPhoto($chatId, $logoUrl, $message, $keyboard);

if ($result) {
    echo "<div class='banner'>\n";
    echo "<p class='success'>🎉 <strong>Успех!</strong> Золотистый баннер успешно отправлен!</p>\n";
    echo "</div>\n";
    
    // Получаем file_id для будущего использования
    if (isset($result['photo']) && is_array($result['photo'])) {
        $fileId = end($result['photo'])['file_id']; // Берем самое большое разрешение
        echo "<p class='info'>🆔 File ID: <code>{$fileId}</code></p>\n";
        echo "<p class='info'>💡 Этот file_id можно использовать для быстрой отправки без URL</p>\n";
        botLog("SUCCESS: Золотистый баннер отправлен. File ID: {$fileId}");
    }
    
    echo "<p class='success'>✅ Теперь можете протестировать бота командой /start</p>\n";
    echo "<p class='success'>✅ Новый золотистый баннер должен появиться в приветственном сообщении!</p>\n";
    
} else {
    echo "<div class='error'>\n";
    echo "<p>❌ <strong>Ошибка!</strong> Не удалось отправить золотистый баннер.</p>\n";
    echo "</div>\n";
    
    echo "<p class='info'>💡 Возможные причины:</p>\n";
    echo "<ul>\n";
    echo "<li>Неверный chat_id</li>\n";
    echo "<li>Картинка недоступна по URL</li>\n";
    echo "<li>Проблемы с Telegram API</li>\n";
    echo "<li>Бот заблокирован пользователем</li>\n";
    echo "</ul>\n";
    
    botLog("ERROR: Не удалось отправить золотистый баннер для chat_id: {$chatId}");
}

// Дополнительная информация
echo "<hr style='border-color:#333;'>\n";
echo "<h3 class='info'>📋 Информация о золотистом баннере</h3>\n";

echo "<p><strong>Файл:</strong> bot_welcome_golden_banner.png</p>\n";
echo "<p><strong>SVG исходник:</strong> bot_welcome_golden_banner.svg</p>\n";
echo "<p><strong>Расположение:</strong> images/bot_welcome_golden_banner.png</p>\n";
echo "<p><strong>Прямая ссылка:</strong> <a href='https://app.uniqpaid.com/test3/images/bot_welcome_golden_banner.png' target='_blank'>Открыть картинку</a></p>\n";

echo "<h3 class='info'>🎨 Особенности дизайна</h3>\n";
echo "<ul>\n";
echo "<li>🌟 Премиальные золотистые градиенты</li>\n";
echo "<li>🚫 Никаких размытий - четкие контуры</li>\n";
echo "<li>💎 Актуальные криптовалюты: TON, USDT, BTC, ETH</li>\n";
echo "<li>🏆 Объемные 3D эффекты</li>\n";
echo "<li>✨ Элегантные соединительные линии</li>\n";
echo "<li>🎯 Информационные блоки с премиальными тенями</li>\n";
echo "</ul>\n";

echo "<h3 class='info'>🔧 Следующие шаги</h3>\n";
echo "<ol>\n";
echo "<li>Конвертируйте SVG в PNG (размер 800x400 или больше)</li>\n";
echo "<li>Загрузите PNG файл как <code>bot_welcome_golden_banner.png</code></li>\n";
echo "<li>Протестируйте бота командой /start</li>\n";
echo "<li>Наслаждайтесь новым золотистым дизайном! 🌟</li>\n";
echo "</ol>\n";

echo "</body></html>\n";
?>
