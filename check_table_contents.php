<?php
echo "=== ПРОВЕРКА СОДЕРЖИМОГО ТАБЛИЦ ===\n\n";

require_once 'app/Config/config.php';
require_once 'app/Core/Autoloader.php';

$autoloader = new App\Core\Autoloader();
$autoloader->register();

use App\Core\Database;

Database::init();

echo "✅ База данных инициализирована\n\n";

// Проверяем содержимое таблицы settings
echo "📊 ТАБЛИЦА SETTINGS:\n";
try {
    $settings = Database::query("SELECT * FROM settings");
    echo "Всего записей в settings: " . count($settings) . "\n";
    
    if (count($settings) > 0) {
        echo "Первые 5 записей:\n";
        for ($i = 0; $i < min(5, count($settings)); $i++) {
            $setting = $settings[$i];
            echo "  - {$setting['category']}.{$setting['key']} = " . substr($setting['value'], 0, 30) . "\n";
        }
        
        // Группируем по категориям
        $categories = [];
        foreach ($settings as $setting) {
            $categories[$setting['category']][] = $setting;
        }
        
        echo "\nПо категориям:\n";
        foreach ($categories as $category => $categorySettings) {
            echo "  - $category: " . count($categorySettings) . " настроек\n";
        }
    } else {
        echo "❌ Таблица settings ПУСТАЯ!\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка чтения settings: " . $e->getMessage() . "\n";
}

// Проверяем содержимое таблицы users
echo "\n👥 ТАБЛИЦА USERS:\n";
try {
    $usersCount = Database::query("SELECT COUNT(*) as cnt FROM users");
    $totalUsers = $usersCount[0]['cnt'] ?? 0;
    echo "Всего записей в users: $totalUsers\n";
    
    if ($totalUsers > 0) {
        $users = Database::query("SELECT * FROM users LIMIT 5");
        echo "Первые 5 пользователей:\n";
        foreach ($users as $user) {
            echo "  - {$user['telegram_id']}: {$user['first_name']} {$user['last_name']}\n";
        }
    } else {
        echo "❌ Таблица users ПУСТАЯ!\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка чтения users: " . $e->getMessage() . "\n";
}

// Проверяем содержимое таблицы admin_users
echo "\n🔐 ТАБЛИЦА ADMIN_USERS:\n";
try {
    $admins = Database::query("SELECT * FROM admin_users");
    echo "Всего записей в admin_users: " . count($admins) . "\n";
    
    if (count($admins) > 0) {
        foreach ($admins as $admin) {
            echo "  - {$admin['username']} (ID: {$admin['id']})\n";
        }
    } else {
        echo "❌ Таблица admin_users ПУСТАЯ!\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка чтения admin_users: " . $e->getMessage() . "\n";
}

// Проверяем структуру таблицы settings
echo "\n🏗️ СТРУКТУРА ТАБЛИЦЫ SETTINGS:\n";
try {
    $structure = Database::query("PRAGMA table_info(settings)");
    foreach ($structure as $column) {
        echo "  - {$column['name']} ({$column['type']})\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка получения структуры: " . $e->getMessage() . "\n";
}

echo "\n🎯 РЕЗУЛЬТАТ:\n";
echo "Если таблицы пустые - нужно заново импортировать данные\n";
echo "Если таблицы полные - проблема в моделях\n";

echo "\n=== КОНЕЦ ПРОВЕРКИ ===\n";
?>
