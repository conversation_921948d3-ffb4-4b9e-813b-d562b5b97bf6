<?php
declare(strict_types=1);

namespace App\Core;

/**
 * Database management class
 * Uses SQLite database with built-in PHP PDO/SQLite3
 * Includes development fallback for local testing
 */
class Database
{
    private static ?object $connection = null;
    private static bool $initialized = false;
    private static bool $usingSQLite = false;

    // Development data storage
    private static array $devData = [
        'texts' => [],
        'settings' => []
    ];

    /**
     * Public method to initialize database
     */
    public static function init(): void
    {
        self::initInternal();
    }

    /**
     * Initialize database internally
     */
    private static function initInternal(): void
    {
        if (self::$initialized) {
            return;
        }

        self::$initialized = true;

        // Check if we should use dev mode (file storage)
        if (defined('DEV_MODE') && DEV_MODE === true) {
            // Force file storage mode
            self::$usingSQLite = false;
            self::initDevMode();
            return;
        }

        // Try to initialize SQLite
        if (self::isSQLiteAvailable()) {
            try {
                self::initSQLite();
                self::$usingSQLite = true;
                return;
            } catch (\Exception $e) {
                // SQLite failed, fall back to file storage
                self::$usingSQLite = false;
                self::initDevMode();
                return;
            }
        }

        // No SQLite available, use file storage
        self::$usingSQLite = false;
        self::initDevMode();
    }

    /**
     * Check if SQLite is available
     */
    private static function isSQLiteAvailable(): bool
    {
        // Try PDO SQLite first
        if (class_exists('PDO')) {
            try {
                $drivers = \PDO::getAvailableDrivers();
                if (in_array('sqlite', $drivers)) {
                    return true;
                }
            } catch (\Exception $e) {
                // Continue to next check
            }
        }

        // Try SQLite3 class
        if (class_exists('SQLite3')) {
            return true;
        }

        // If no SQLite available, we'll work with file directly
        return false;
    }

    /**
     * Initialize SQLite database
     */
    private static function initSQLite(): void
    {
        try {
            $dbDir = dirname(DB_PATH);
            if (!is_dir($dbDir)) {
                mkdir($dbDir, 0755, true);
            }

            // Try PDO SQLite first
            if (class_exists('PDO') && in_array('sqlite', \PDO::getAvailableDrivers())) {
                self::$connection = new \PDO('sqlite:' . DB_PATH);
                self::$connection->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
                self::$connection->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);
                self::$connection->exec('PRAGMA foreign_keys = ON');
                self::$usingSQLite = true;
            }
            // Try SQLite3 class
            elseif (class_exists('SQLite3')) {
                self::$connection = new \SQLite3(DB_PATH);
                self::$connection->enableExceptions(true);
                self::$connection->exec('PRAGMA foreign_keys = ON');
                self::$usingSQLite = true;
            }

            if (self::$usingSQLite) {
                self::createTables();
                self::insertDefaultData();
            }

        } catch (\Exception $e) {
            self::$usingSQLite = false;
            self::initDevMode();
        }
    }

    /**
     * Initialize development mode with file-based storage
     */
    private static function initDevMode(): void
    {
        self::$usingSQLite = false;
        self::$connection = (object)['dev' => true];

        // Load texts from JSON files (REFERENCE)
        self::loadTextsFromFiles();

        // Initialize admin users in dev mode
        self::initDevAdmins();

        // Initialize settings in dev mode
        self::initDevSettings();
    }

    /**
     * Load texts from REFERENCE JSON files for dev mode
     */
    private static function loadTextsFromFiles(): void
    {
        $ruFile = __DIR__ . '/../../REFERENCE/locales/ru.json';
        $enFile = __DIR__ . '/../../REFERENCE/locales/en.json';

        if (!file_exists($ruFile) || !file_exists($enFile)) {
            // Fallback to minimal data
            self::$devData['texts'] = self::getDefaultTextsArray();
            return;
        }

        try {
            $ruTexts = json_decode(file_get_contents($ruFile), true);
            $enTexts = json_decode(file_get_contents($enFile), true);

            if (!$ruTexts || !$enTexts) {
                self::$devData['texts'] = self::getDefaultTextsArray();
                return;
            }

            // Convert JSON structure to database-like format
            $allTexts = [];
            $id = 1;

            // Process Russian texts
            $allTexts = array_merge($allTexts, self::convertJsonToDbFormat($ruTexts, 'ru', $id));
            $id = count($allTexts) + 1;

            // Process English texts
            $allTexts = array_merge($allTexts, self::convertJsonToDbFormat($enTexts, 'en', $id));

            self::$devData['texts'] = $allTexts;

        } catch (\Exception $e) {
            self::$devData['texts'] = self::getDefaultTextsArray();
        }
    }

    /**
     * Initialize admin users for dev mode
     */
    private static function initDevAdmins(): void
    {
        // Create admin users file path
        $adminFile = __DIR__ . '/../../database/admin_users.json';
        $adminDir = dirname($adminFile);

        if (!is_dir($adminDir)) {
            mkdir($adminDir, 0755, true);
        }

        // Load existing admins or create default
        if (file_exists($adminFile)) {
            $admins = json_decode(file_get_contents($adminFile), true);
            if (!$admins) {
                $admins = [];
            }
        } else {
            $admins = [];
        }

        // Add default admin if not exists
        $adminExists = false;
        foreach ($admins as $admin) {
            if ($admin['username'] === 'admin') {
                $adminExists = true;
                break;
            }
        }

        if (!$adminExists) {
            $admins[] = [
                'id' => 1,
                'username' => 'admin',
                'password_hash' => password_hash('admin', PASSWORD_DEFAULT),
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'last_login' => null
            ];

            // Save to file
            file_put_contents($adminFile, json_encode($admins, JSON_PRETTY_PRINT));
        }

        self::$devData['admin_users'] = $admins;
    }

    /**
     * Initialize settings for dev mode
     */
    private static function initDevSettings(): void
    {
        $settingsFile = __DIR__ . '/../../database/settings.json';
        $settings = [];

        // Load existing settings if file exists
        if (file_exists($settingsFile)) {
            $content = file_get_contents($settingsFile);
            $settings = json_decode($content, true) ?: [];
        }

        self::$devData['settings'] = $settings;
    }

    /**
     * Convert JSON structure to database format
     */
    private static function convertJsonToDbFormat(array $texts, string $language, int &$startId): array
    {
        $result = [];
        $currentTime = gmdate('Y-m-d H:i:s');

        // Bot texts
        if (isset($texts['bot'])) {
            foreach ($texts['bot'] as $key => $value) {
                $result[] = [
                    'id' => $startId++,
                    'type' => 'bot',
                    'language' => $language,
                    'key' => $key,
                    'value' => $value,
                    'description' => "Bot text: $key",
                    'created_at' => $currentTime,
                    'updated_at' => $currentTime
                ];
            }
        }

        // App texts (convert to miniapp)
        if (isset($texts['app'])) {
            $result = array_merge($result, self::convertNestedToDbFormat($texts['app'], 'miniapp', $language, '', $startId, $currentTime));
        }

        return $result;
    }

    /**
     * Convert nested JSON to database format
     */
    private static function convertNestedToDbFormat(array $data, string $type, string $language, string $prefix, int &$id, string $currentTime): array
    {
        $result = [];

        foreach ($data as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                $result = array_merge($result, self::convertNestedToDbFormat($value, $type, $language, $fullKey, $id, $currentTime));
            } else {
                $result[] = [
                    'id' => $id++,
                    'type' => $type,
                    'language' => $language,
                    'key' => $fullKey,
                    'value' => $value,
                    'description' => "App text: $fullKey",
                    'created_at' => $currentTime,
                    'updated_at' => $currentTime
                ];
            }
        }

        return $result;
    }

    /**
     * Create database tables
     */
    private static function createTables(): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS texts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL CHECK (type IN ('bot', 'miniapp')),
                language TEXT NOT NULL CHECK (language IN ('ru', 'en')),
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT (datetime('now', 'utc')),
                updated_at DATETIME DEFAULT (datetime('now', 'utc')),
                UNIQUE(type, language, key)
            );

            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                telegram_id INTEGER UNIQUE NOT NULL,
                username TEXT,
                first_name TEXT,
                last_name TEXT,
                balance INTEGER DEFAULT 0,
                total_earned INTEGER DEFAULT 0,
                language TEXT DEFAULT 'ru',
                referral_code TEXT UNIQUE,
                referred_by INTEGER,
                referrals_count INTEGER DEFAULT 0,
                referral_earnings INTEGER DEFAULT 0,
                withdrawals_count INTEGER DEFAULT 0,
                suspicious_activity INTEGER DEFAULT 0,
                blocked INTEGER DEFAULT 0,
                last_activity DATETIME,
                created_at DATETIME DEFAULT (datetime('now', 'utc')),
                updated_at DATETIME DEFAULT (datetime('now', 'utc')),
                FOREIGN KEY (referred_by) REFERENCES users(id)
            );

            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'admin',
                is_active INTEGER DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT (datetime('now', 'utc')),
                updated_at DATETIME DEFAULT (datetime('now', 'utc'))
            );

            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL CHECK (category IN ('bot', 'miniapp', 'nowpayments')),
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                type TEXT NOT NULL CHECK (type IN ('string', 'number', 'boolean', 'json')),
                description TEXT,
                created_at DATETIME DEFAULT (datetime('now', 'utc')),
                updated_at DATETIME DEFAULT (datetime('now', 'utc')),
                UNIQUE(category, key)
            );
        ";

        if (self::$connection instanceof \PDO) {
            self::$connection->exec($sql);
        } else {
            self::$connection->exec($sql);
        }
    }

    /**
     * Insert default data from REFERENCE
     */
    private static function insertDefaultData(): void
    {
        // Check if texts already exist
        $count = self::queryOne("SELECT COUNT(*) as count FROM texts");
        if ($count && $count['count'] > 0) {
            return; // Data already exists
        }

        // Load texts from REFERENCE JSON files
        self::loadTextsFromReference();
    }

    /**
     * Load all texts from REFERENCE JSON files
     */
    private static function loadTextsFromReference(): void
    {
        $ruFile = __DIR__ . '/../../REFERENCE/locales/ru.json';
        $enFile = __DIR__ . '/../../REFERENCE/locales/en.json';

        if (!file_exists($ruFile) || !file_exists($enFile)) {
            // Fallback to minimal default data if REFERENCE files not found
            self::insertMinimalDefaultData();
            return;
        }

        $ruTexts = json_decode(file_get_contents($ruFile), true);
        $enTexts = json_decode(file_get_contents($enFile), true);

        if (!$ruTexts || !$enTexts) {
            self::insertMinimalDefaultData();
            return;
        }

        // Import Russian texts
        self::importLanguageTexts($ruTexts, 'ru');

        // Import English texts
        self::importLanguageTexts($enTexts, 'en');

        // Insert default admin user and settings
        self::insertDefaultAdminAndSettings();
    }

    /**
     * Import texts for a specific language
     */
    private static function importLanguageTexts(array $texts, string $language): void
    {
        // Import bot texts
        if (isset($texts['bot'])) {
            foreach ($texts['bot'] as $key => $value) {
                self::execute(
                    "INSERT OR IGNORE INTO texts (type, language, key, value, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                    ['bot', $language, $key, $value, "Bot text: $key"]
                );
            }
        }

        // Import app texts (rename to miniapp)
        if (isset($texts['app'])) {
            self::importNestedTexts($texts['app'], 'miniapp', $language, '');
        }
    }

    /**
     * Recursively import nested texts
     */
    private static function importNestedTexts(array $data, string $type, string $language, string $prefix): void
    {
        foreach ($data as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                // Recursively process nested arrays
                self::importNestedTexts($value, $type, $language, $fullKey);
            } else {
                // Add text
                self::execute(
                    "INSERT OR IGNORE INTO texts (type, language, key, value, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                    [$type, $language, $fullKey, $value, "App text: $fullKey"]
                );
            }
        }
    }

    /**
     * Insert minimal default data as fallback
     */
    private static function insertMinimalDefaultData(): void
    {
        $texts = self::getDefaultTextsArray();
        foreach ($texts as $text) {
            self::execute(
                "INSERT OR IGNORE INTO texts (type, language, key, value, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                [$text['type'], $text['language'], $text['key'], $text['value'], $text['description']]
            );
        }
    }

    /**
     * Execute a query and return results
     */
    public static function query(string $sql, array $params = []): array
    {
        self::initInternal();

        if (!self::$usingSQLite) {
            return self::queryDev($sql, $params);
        }

        if (self::$connection instanceof \PDO) {
            $stmt = self::$connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } else {
            // SQLite3
            if (empty($params)) {
                $result = self::$connection->query($sql);
            } else {
                $stmt = self::$connection->prepare($sql);
                foreach ($params as $index => $value) {
                    $stmt->bindValue($index + 1, $value);
                }
                $result = $stmt->execute();
            }

            $rows = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $rows[] = $row;
            }

            return $rows;
        }
    }

    /**
     * Execute a query and return single row
     */
    public static function queryOne(string $sql, array $params = []): ?array
    {
        $results = self::query($sql, $params);
        return $results[0] ?? null;
    }

    /**
     * Execute an INSERT/UPDATE/DELETE query
     */
    public static function execute(string $sql, array $params = []): int
    {
        self::initInternal();

        if (!self::$usingSQLite) {
            return self::executeDev($sql, $params);
        }

        if (self::$connection instanceof \PDO) {
            $stmt = self::$connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } else {
            // SQLite3
            if (empty($params)) {
                self::$connection->exec($sql);
                return self::$connection->changes();
            } else {
                $stmt = self::$connection->prepare($sql);
                foreach ($params as $index => $value) {
                    $stmt->bindValue($index + 1, $value);
                }
                $stmt->execute();
                return self::$connection->changes();
            }
        }
    }

    /**
     * Get last insert ID
     */
    public static function lastInsertId(): int
    {
        self::initInternal();

        if (!self::$usingSQLite) {
            return count(self::$devData['texts']); // Mock ID
        }

        if (self::$connection instanceof \PDO) {
            return (int) self::$connection->lastInsertId();
        } else {
            return self::$connection->lastInsertRowID();
        }
    }

    /**
     * Get database file path
     */
    public static function getDatabasePath(): string
    {
        return DB_PATH;
    }

    /**
     * Development mode query simulation
     */
    private static function queryDev(string $sql, array $params = []): array
    {
        // Handle admin_users table queries
        if (stripos($sql, 'SELECT') === 0 && stripos($sql, 'admin_users') !== false) {
            $admins = self::$devData['admin_users'] ?? [];

            // Handle COUNT queries
            if (stripos($sql, 'COUNT') !== false) {
                $count = 0;
                if (!empty($params) && stripos($sql, 'WHERE') !== false) {
                    // Count with WHERE clause
                    foreach ($admins as $admin) {
                        if ($admin['username'] === $params[0]) {
                            $count++;
                        }
                    }
                } else {
                    $count = count($admins);
                }
                return [['count' => $count]];
            }

            // Handle specific user queries
            if (!empty($params)) {
                foreach ($admins as $admin) {
                    if ($admin['username'] === $params[0]) {
                        return [$admin];
                    }
                }
                return [];
            }

            return $admins;
        }

        // Handle sqlite_master queries
        if (stripos($sql, 'sqlite_master') !== false) {
            return [
                ['name' => 'admin_users'],
                ['name' => 'texts'],
                ['name' => 'users'],
                ['name' => 'settings']
            ];
        }

        // Handle PRAGMA queries
        if (stripos($sql, 'PRAGMA') !== false) {
            if (stripos($sql, 'settings') !== false) {
                return [
                    ['cid' => 0, 'name' => 'id', 'type' => 'INTEGER', 'notnull' => 0, 'dflt_value' => null, 'pk' => 1],
                    ['cid' => 1, 'name' => 'category', 'type' => 'TEXT', 'notnull' => 1, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 2, 'name' => 'key', 'type' => 'TEXT', 'notnull' => 1, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 3, 'name' => 'value', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 4, 'name' => 'type', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => 'string', 'pk' => 0],
                    ['cid' => 5, 'name' => 'description', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 6, 'name' => 'created_at', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 7, 'name' => 'updated_at', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => null, 'pk' => 0]
                ];
            } else {
                return [
                    ['cid' => 0, 'name' => 'id', 'type' => 'INTEGER', 'notnull' => 0, 'dflt_value' => null, 'pk' => 1],
                    ['cid' => 1, 'name' => 'username', 'type' => 'TEXT', 'notnull' => 1, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 2, 'name' => 'password_hash', 'type' => 'TEXT', 'notnull' => 1, 'dflt_value' => null, 'pk' => 0],
                    ['cid' => 3, 'name' => 'email', 'type' => 'TEXT', 'notnull' => 0, 'dflt_value' => null, 'pk' => 0]
                ];
            }
        }

        // Handle settings table queries
        if (stripos($sql, 'SELECT') === 0 && stripos($sql, 'settings') !== false) {
            $settings = self::$devData['settings'] ?? [];

            // Handle GROUP BY queries for statistics
            if (stripos($sql, 'GROUP BY') !== false && stripos($sql, 'COUNT') !== false) {
                $stats = [];
                foreach ($settings as $setting) {
                    $category = $setting['category'] ?? '';
                    if (!isset($stats[$category])) {
                        $stats[$category] = [
                            'category' => $category,
                            'count' => 0
                        ];
                    }
                    $stats[$category]['count']++;
                }
                return array_values($stats);
            }

            // Handle COUNT queries
            if (stripos($sql, 'COUNT') !== false) {
                $count = count($settings);

                // Apply WHERE filtering for COUNT
                if (!empty($params) && stripos($sql, 'WHERE') !== false) {
                    $filtered = [];
                    foreach ($settings as $setting) {
                        if (isset($setting['category']) && $setting['category'] === $params[0]) {
                            $filtered[] = $setting;
                        }
                    }
                    $count = count($filtered);
                }

                return [['cnt' => $count]];
            }

            // Apply WHERE filtering
            if (!empty($params) && stripos($sql, 'WHERE') !== false) {
                $filtered = [];
                foreach ($settings as $setting) {
                    $match = true;

                    // Simple parameter matching for category
                    if (count($params) >= 1 && isset($setting['category']) && $setting['category'] !== $params[0]) {
                        $match = false;
                    }

                    // Additional parameter matching for key
                    if (count($params) >= 2 && isset($setting['key']) && $setting['key'] !== $params[1]) {
                        $match = false;
                    }

                    if ($match) {
                        $filtered[] = $setting;
                    }
                }
                return $filtered;
            }

            return $settings;
        }

        // Simple simulation for texts table
        if (stripos($sql, 'SELECT') === 0 && stripos($sql, 'texts') !== false) {
            $texts = self::$devData['texts'];

            // Handle GROUP BY queries for statistics
            if (stripos($sql, 'GROUP BY') !== false && stripos($sql, 'COUNT') !== false) {
                $stats = [];
                foreach ($texts as $text) {
                    $key = $text['type'] . '_' . $text['language'];
                    if (!isset($stats[$key])) {
                        $stats[$key] = [
                            'type' => $text['type'],
                            'language' => $text['language'],
                            'count' => 0
                        ];
                    }
                    $stats[$key]['count']++;
                }
                return array_values($stats);
            }

            // Apply basic filtering
            if (!empty($params)) {
                $filtered = [];
                foreach ($texts as $text) {
                    $match = true;

                    // Simple parameter matching
                    if (count($params) >= 1 && isset($text['type']) && $text['type'] !== $params[0]) {
                        $match = false;
                    }
                    if (count($params) >= 2 && isset($text['language']) && $text['language'] !== $params[1]) {
                        $match = false;
                    }

                    if ($match) {
                        $filtered[] = $text;
                    }
                }
                return $filtered;
            }

            return $texts;
        }

        return [];
    }

    /**
     * Development mode execute simulation
     */
    private static function executeDev(string $sql, array $params = []): int
    {
        // Handle admin_users table operations
        if (stripos($sql, 'admin_users') !== false) {
            $adminFile = __DIR__ . '/../../database/admin_users.json';
            $admins = self::$devData['admin_users'] ?? [];

            if (stripos($sql, 'INSERT') === 0) {
                // Add new admin
                $newAdmin = [
                    'id' => count($admins) + 1,
                    'username' => $params[0] ?? 'admin',
                    'password_hash' => $params[1] ?? password_hash('admin', PASSWORD_DEFAULT),
                    'email' => $params[2] ?? '<EMAIL>',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'last_login' => null
                ];

                $admins[] = $newAdmin;
                self::$devData['admin_users'] = $admins;

                // Save to file
                file_put_contents($adminFile, json_encode($admins, JSON_PRETTY_PRINT));
                return 1;
            }

            if (stripos($sql, 'UPDATE') === 0) {
                // Update admin
                foreach ($admins as &$admin) {
                    if (isset($params[0]) && $admin['id'] == $params[0]) {
                        $admin['last_login'] = date('Y-m-d H:i:s');
                        $admin['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                self::$devData['admin_users'] = $admins;
                file_put_contents($adminFile, json_encode($admins, JSON_PRETTY_PRINT));
                return 1;
            }

            if (stripos($sql, 'DELETE') === 0) {
                // Delete admin
                $admins = array_filter($admins, function($admin) use ($params) {
                    return $admin['username'] !== ($params[0] ?? 'admin');
                });

                self::$devData['admin_users'] = array_values($admins);
                file_put_contents($adminFile, json_encode(array_values($admins), JSON_PRETTY_PRINT));
                return 1;
            }
        }

        // Handle settings table operations
        if (stripos($sql, 'settings') !== false) {
            $settingsFile = __DIR__ . '/../../database/settings.json';
            $settings = self::$devData['settings'] ?? [];

            if (stripos($sql, 'INSERT') === 0) {
                // Add new setting
                $newSetting = [
                    'id' => count($settings) + 1,
                    'category' => $params[0] ?? '',
                    'key' => $params[1] ?? '',
                    'value' => $params[2] ?? '',
                    'type' => $params[3] ?? 'string',
                    'description' => $params[4] ?? '',
                    'created_at' => gmdate('Y-m-d H:i:s'),
                    'updated_at' => gmdate('Y-m-d H:i:s')
                ];

                $settings[] = $newSetting;
                self::$devData['settings'] = $settings;

                // Save to file
                $settingsDir = dirname($settingsFile);
                if (!is_dir($settingsDir)) {
                    mkdir($settingsDir, 0755, true);
                }
                file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));
                return 1;
            }

            if (stripos($sql, 'UPDATE') === 0) {
                // Update existing setting
                $updated = false;
                foreach ($settings as &$setting) {
                    // Check if this is the setting we want to update
                    // UPDATE settings SET value = ?, type = ?, description = ?, updated_at = datetime('now', 'utc') WHERE category = ? AND key = ?
                    if (count($params) >= 5 &&
                        $setting['category'] === $params[3] &&
                        $setting['key'] === $params[4]) {

                        $setting['value'] = $params[0];
                        $setting['type'] = $params[1];
                        $setting['description'] = $params[2];
                        $setting['updated_at'] = gmdate('Y-m-d H:i:s');
                        $updated = true;
                        break;
                    }
                }

                if ($updated) {
                    self::$devData['settings'] = $settings;

                    // Save to file
                    $settingsDir = dirname($settingsFile);
                    if (!is_dir($settingsDir)) {
                        mkdir($settingsDir, 0755, true);
                    }
                    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));
                    return 1;
                }
                return 0;
            }

            if (stripos($sql, 'DELETE') === 0) {
                // Clear all settings
                self::$devData['settings'] = [];
                if (file_exists($settingsFile)) {
                    file_put_contents($settingsFile, json_encode([], JSON_PRETTY_PRINT));
                }
                return 1;
            }
        }

        // Handle CREATE TABLE operations
        if (stripos($sql, 'CREATE TABLE') === 0) {
            return 1; // Simulate table creation success
        }

        // Handle DROP TABLE operations
        if (stripos($sql, 'DROP TABLE') !== false) {
            if (stripos($sql, 'settings') !== false) {
                self::$devData['settings'] = [];
                $settingsFile = __DIR__ . '/../../database/settings.json';
                if (file_exists($settingsFile)) {
                    unlink($settingsFile);
                }
            }
            return 1;
        }

        // Simulate other INSERT/UPDATE operations
        if (stripos($sql, 'INSERT') === 0 || stripos($sql, 'UPDATE') === 0) {
            return 1; // Simulate success
        }

        return 0;
    }

    /**
     * Get default texts data array
     */
    private static function getDefaultTextsArray(): array
    {
        $currentTime = gmdate('Y-m-d H:i:s');

        return [
            // Bot texts Russian
            ['id' => 1, 'type' => 'bot', 'language' => 'ru', 'key' => 'welcome_title', 'value' => '🎉 Добро пожаловать в UniQPaid!', 'description' => 'Welcome title', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 2, 'type' => 'bot', 'language' => 'ru', 'key' => 'welcome_subtitle', 'value' => '💰 Просто смотри рекламу сейчас и получай крипту сразу!', 'description' => 'Welcome subtitle', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 3, 'type' => 'bot', 'language' => 'ru', 'key' => 'main_menu_hello', 'value' => '👋 Привет, {firstName}!', 'description' => 'Main menu greeting', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 4, 'type' => 'bot', 'language' => 'ru', 'key' => 'launch_app_button', 'value' => '🚀 Запустить приложение', 'description' => 'Launch app button', 'created_at' => $currentTime, 'updated_at' => $currentTime],

            // Bot texts English
            ['id' => 5, 'type' => 'bot', 'language' => 'en', 'key' => 'welcome_title', 'value' => '🎉 Welcome to UniQPaid!', 'description' => 'Welcome title', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 6, 'type' => 'bot', 'language' => 'en', 'key' => 'welcome_subtitle', 'value' => '💰 Just watch ads now and get crypto instantly!', 'description' => 'Welcome subtitle', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 7, 'type' => 'bot', 'language' => 'en', 'key' => 'main_menu_hello', 'value' => '👋 Hello, {firstName}!', 'description' => 'Main menu greeting', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 8, 'type' => 'bot', 'language' => 'en', 'key' => 'launch_app_button', 'value' => '🚀 Launch', 'description' => 'Launch app button', 'created_at' => $currentTime, 'updated_at' => $currentTime],

            // MiniApp texts Russian
            ['id' => 9, 'type' => 'miniapp', 'language' => 'ru', 'key' => 'app_title', 'value' => 'UniQPaid', 'description' => 'Application title', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 10, 'type' => 'miniapp', 'language' => 'ru', 'key' => 'nav_home', 'value' => 'Главная', 'description' => 'Home navigation', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 11, 'type' => 'miniapp', 'language' => 'ru', 'key' => 'nav_earnings', 'value' => 'Заработок', 'description' => 'Earnings navigation', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 12, 'type' => 'miniapp', 'language' => 'ru', 'key' => 'loading', 'value' => 'Загрузка...', 'description' => 'Loading text', 'created_at' => $currentTime, 'updated_at' => $currentTime],

            // MiniApp texts English
            ['id' => 13, 'type' => 'miniapp', 'language' => 'en', 'key' => 'app_title', 'value' => 'UniQPaid', 'description' => 'Application title', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 14, 'type' => 'miniapp', 'language' => 'en', 'key' => 'nav_home', 'value' => 'Home', 'description' => 'Home navigation', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 15, 'type' => 'miniapp', 'language' => 'en', 'key' => 'nav_earnings', 'value' => 'Earnings', 'description' => 'Earnings navigation', 'created_at' => $currentTime, 'updated_at' => $currentTime],
            ['id' => 16, 'type' => 'miniapp', 'language' => 'en', 'key' => 'loading', 'value' => 'Loading...', 'description' => 'Loading text', 'created_at' => $currentTime, 'updated_at' => $currentTime],
        ];
    }

    /**
     * Insert default admin user and settings
     */
    private static function insertDefaultAdminAndSettings(): void
    {
        // Insert default admin user (admin/admin)
        $adminExists = self::queryOne("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
        if (!$adminExists || $adminExists['count'] == 0) {
            self::execute(
                "INSERT INTO admin_users (username, password_hash, email, created_at, updated_at) VALUES (?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                ['admin', password_hash('admin', PASSWORD_DEFAULT), '<EMAIL>']
            );
        }

        // Insert default settings
        self::insertDefaultSettings();
    }

    /**
     * Insert default settings
     */
    private static function insertDefaultSettings(): void
    {
        $defaultSettings = [
            // Bot settings (from reference)
            ['bot', 'telegram_bot_token', '**********************************************', 'string', 'Telegram Bot Token'],
            ['bot', 'bot_username', 'uniqpaid_paid_bot', 'string', 'Bot Username'],
            ['bot', 'webapp_url', 'https://app.uniqpaid.com/test4', 'string', 'WebApp URL'],
            ['bot', 'webhook_url', 'https://app.uniqpaid.com/test4/bot/webhook.php', 'string', 'Webhook URL'],
            ['bot', 'support_bot_token', '7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M', 'string', 'Support Bot Token'],
            ['bot', 'support_bot_username', 'uniqpaid_support_bot', 'string', 'Support Bot Username'],
            ['bot', 'support_webhook_url', 'https://app.uniqpaid.com/test4/api/admin/support_webhook.php', 'string', 'Support Webhook URL'],
            ['bot', 'admin_user_ids', '5880288830', 'string', 'Admin User IDs (comma separated)'],

            // MiniApp settings (from reference)
            ['miniapp', 'app_name', 'UniQPaid', 'string', 'Application name'],
            ['miniapp', 'app_url', 'https://app.uniqpaid.com/test4', 'string', 'Application URL'],
            ['miniapp', 'conversion_rate', '0.001', 'number', 'Exchange rate (coins to USD)'],
            ['miniapp', 'min_withdrawal_amount', '100', 'number', 'Minimum withdrawal amount'],
            ['miniapp', 'min_balance_for_withdrawal', '1', 'number', 'Minimum balance for withdrawal access'],
            ['miniapp', 'show_fees_to_user', '1', 'boolean', 'Show fees to user in calculator'],
            ['miniapp', 'ad_view_reward', '1', 'number', 'Base ad view reward'],
            ['miniapp', 'referral_bonus_percent', '0.1', 'number', 'Referral bonus percentage (0.1 = 10%)'],

            // Ad rewards settings
            ['miniapp', 'ad_reward_native_banner', '10', 'number', 'Reward for native banner ads'],
            ['miniapp', 'ad_reward_interstitial', '8', 'number', 'Reward for interstitial ads'],
            ['miniapp', 'ad_reward_rewarded_video', '2', 'number', 'Reward for rewarded video ads'],

            // Ad limits settings
            ['miniapp', 'user_ad_limit_native_banner', '10', 'number', 'Daily limit for native banner ads per user'],
            ['miniapp', 'user_ad_limit_interstitial', '10', 'number', 'Daily limit for interstitial ads per user'],
            ['miniapp', 'user_ad_limit_rewarded_video', '20', 'number', 'Daily limit for rewarded video ads per user'],

            // RichAds settings
            ['miniapp', 'richads_pub_id', '944840', 'string', 'RichAds Publisher ID'],
            ['miniapp', 'richads_app_id', '2122', 'string', 'RichAds Application ID'],
            ['miniapp', 'richads_debug_mode', '0', 'boolean', 'RichAds Debug Mode'],

            // NOWPayments settings
            ['miniapp', 'nowpayments_email', '<EMAIL>', 'string', 'NOWPayments Email'],
            ['miniapp', 'nowpayments_password', 'Yjen10,er20', 'string', 'NOWPayments Password'],
        ];

        foreach ($defaultSettings as $setting) {
            self::execute(
                "INSERT OR IGNORE INTO settings (category, key, value, type, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'utc'), datetime('now', 'utc'))",
                $setting
            );
        }
    }
}
