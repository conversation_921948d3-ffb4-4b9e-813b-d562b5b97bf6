<?php
/**
 * update_image.php
 * Быстрое обновление картинки бота
 */

require_once __DIR__ . '/config.php';

// Получаем chat_id из параметров или используем тестовый
$chatId = $_GET['chat_id'] ?? null;

if (!$chatId) {
    die("❌ Укажите chat_id в параметре: ?chat_id=YOUR_CHAT_ID");
}

// URL картинки с timestamp для обхода кэша
$logoUrl = 'https://app.uniqpaid.com/test2/images/bot_welcome_super_banner.png?' . time();

// Сообщение для теста
$message = "🔄 <b>Обновление картинки бота</b>\n\n";
$message .= "Тестируем новую версию приветственного баннера.\n";
$message .= "Время обновления: " . date('Y-m-d H:i:s');

// Клавиатура
$keyboard = [
    'inline_keyboard' => [
        [
            [
                'text' => '✅ Картинка обновлена!',
                'callback_data' => 'image_updated'
            ]
        ],
        [
            [
                'text' => '🚀 Запустить приложение',
                'web_app' => ['url' => WEBAPP_URL]
            ]
        ]
    ]
];

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Обновление картинки бота</title></head><body>\n";
echo "<h1>🤖 Обновление картинки Telegram бота</h1>\n";

echo "<p>📤 Отправляем обновленную картинку...</p>\n";
echo "<p>URL: <code>{$logoUrl}</code></p>\n";
echo "<p>Chat ID: <code>{$chatId}</code></p>\n";

// Логируем попытку
botLog("INFO: Попытка обновления картинки для chat_id: {$chatId}");
botLog("INFO: URL картинки: {$logoUrl}");

// Отправляем картинку
$result = sendPhoto($chatId, $logoUrl, $message, $keyboard);

if ($result) {
    echo "<p style='color: green;'>✅ <strong>Успех!</strong> Картинка успешно отправлена.</p>\n";
    
    // Получаем file_id для будущего использования
    if (isset($result['photo']) && is_array($result['photo'])) {
        $fileId = end($result['photo'])['file_id']; // Берем самое большое разрешение
        echo "<p>🆔 File ID: <code>{$fileId}</code></p>\n";
        botLog("SUCCESS: Картинка отправлена. File ID: {$fileId}");
    }
    
    echo "<p>💡 Теперь можете протестировать бота командой /start</p>\n";
    
} else {
    echo "<p style='color: red;'>❌ <strong>Ошибка!</strong> Не удалось отправить картинку.</p>\n";
    echo "<p>Возможные причины:</p>\n";
    echo "<ul>\n";
    echo "<li>Неверный chat_id</li>\n";
    echo "<li>Картинка недоступна по URL</li>\n";
    echo "<li>Проблемы с Telegram API</li>\n";
    echo "</ul>\n";
    
    botLog("ERROR: Не удалось отправить картинку для chat_id: {$chatId}");
    
    // Пробуем отправить текстовое сообщение
    echo "<p>🔄 Пробуем отправить текстовое сообщение...</p>\n";
    $textResult = sendMessage($chatId, "🧪 Тест связи с ботом\n\nЕсли вы видите это сообщение, бот работает, но есть проблемы с картинкой.");
    
    if ($textResult) {
        echo "<p style='color: orange;'>⚠️ Текстовое сообщение отправлено. Проблема именно с картинкой.</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Даже текстовое сообщение не отправилось. Проверьте chat_id и настройки бота.</p>\n";
    }
}

// Дополнительная информация
echo "<hr>\n";
echo "<h3>📋 Дополнительная информация</h3>\n";
echo "<p><strong>Как получить chat_id:</strong></p>\n";
echo "<ol>\n";
echo "<li>Отправьте любое сообщение боту</li>\n";
echo "<li>Посмотрите логи бота в файле <code>bot/bot.log</code></li>\n";
echo "<li>Найдите строку с вашим user_id</li>\n";
echo "</ol>\n";

echo "<p><strong>Использование:</strong></p>\n";
echo "<p><code>https://app.uniqpaid.com/test2/bot/update_image.php?chat_id=YOUR_CHAT_ID</code></p>\n";

echo "<p><strong>Проверка картинки:</strong></p>\n";
echo "<p><a href='https://app.uniqpaid.com/test2/images/bot_welcome_super_banner.png' target='_blank'>Открыть картинку в новой вкладке</a></p>\n";

echo "</body></html>\n";
?>
