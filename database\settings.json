[{"id": 1, "category": "bot", "key": "telegram_bot_token", "value": "**********************************************", "type": "string", "description": "Telegram Bot Token", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 2, "category": "bot", "key": "bot_username", "value": "uniqpaid_paid_bot", "type": "string", "description": "<PERSON><PERSON>", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 3, "category": "bot", "key": "webapp_url", "value": "https://app.uniqpaid.com/test4", "type": "string", "description": "WebApp URL", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 4, "category": "bot", "key": "webhook_url", "value": "https://app.uniqpaid.com/test4/bot/webhook.php", "type": "string", "description": "Webhook URL", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 5, "category": "bot", "key": "support_bot_token", "value": "7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M", "type": "string", "description": "Support Bot Token", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 6, "category": "bot", "key": "support_bot_username", "value": "uniqpaid_support_bot", "type": "string", "description": "Support Bot Username", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 7, "category": "bot", "key": "support_webhook_url", "value": "https://app.uniqpaid.com/test4/api/admin/support_webhook.php", "type": "string", "description": "Support Webhook URL", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 8, "category": "bot", "key": "admin_user_ids", "value": "5880288830", "type": "string", "description": "Admin User IDs (comma separated)", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 9, "category": "miniapp", "key": "app_name", "value": "UniQPaid", "type": "string", "description": "Application name", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 20:51:37"}, {"id": 10, "category": "miniapp", "key": "app_url", "value": "https://app.uniqpaid.com/test4", "type": "string", "description": "Application URL", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:38"}, {"id": 11, "category": "miniapp", "key": "conversion_rate", "value": "0.001", "type": "number", "description": "Exchange rate (coins to USD)", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:38"}, {"id": 12, "category": "miniapp", "key": "min_withdrawal_amount", "value": "100", "type": "number", "description": "Minimum withdrawal amount", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:38"}, {"id": 13, "category": "miniapp", "key": "min_balance_for_withdrawal", "value": "1", "type": "number", "description": "Minimum balance for withdrawal access", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:38"}, {"id": 14, "category": "miniapp", "key": "show_fees_to_user", "value": "1", "type": "boolean", "description": "Show fees to user in calculator", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 15, "category": "miniapp", "key": "ad_view_reward", "value": "1", "type": "number", "description": "Base ad view reward", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 16, "category": "miniapp", "key": "referral_bonus_percent", "value": "0.1", "type": "number", "description": "Referral bonus percentage (0.1 = 10%)", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 17, "category": "miniapp", "key": "ad_reward_native_banner", "value": "10", "type": "number", "description": "Reward for native banner ads", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 18, "category": "miniapp", "key": "ad_reward_interstitial", "value": "8", "type": "number", "description": "Reward for interstitial ads", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 19, "category": "miniapp", "key": "ad_reward_rewarded_video", "value": "2", "type": "number", "description": "Reward for rewarded video ads", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 20, "category": "miniapp", "key": "user_ad_limit_native_banner", "value": "10", "type": "number", "description": "Daily limit for native banner ads per user", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 21, "category": "miniapp", "key": "user_ad_limit_interstitial", "value": "10", "type": "number", "description": "Daily limit for interstitial ads per user", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 22, "category": "miniapp", "key": "user_ad_limit_rewarded_video", "value": "20", "type": "number", "description": "Daily limit for rewarded video ads per user", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 23, "category": "miniapp", "key": "richads_pub_id", "value": "944840", "type": "string", "description": "RichAds Publisher ID", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 24, "category": "miniapp", "key": "richads_app_id", "value": "2122", "type": "string", "description": "RichAds Application ID", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 25, "category": "miniapp", "key": "richads_debug_mode", "value": "0", "type": "boolean", "description": "RichAds Debug Mode", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 12:01:39"}, {"id": 26, "category": "nowpayments", "key": "api_key", "value": "18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7", "type": "string", "description": "API Key (приватный ключ для payout)", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 27, "category": "nowpayments", "key": "public_key", "value": "f6627c2b-98ac-4d30-90dc-c01324330248", "type": "string", "description": "Public Key (публичный ключ для estimate)", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 28, "category": "nowpayments", "key": "ipn_secret", "value": "+dtLfBgWRcW4ybhampqglG39/zxiGgwX", "type": "string", "description": "IPN секретный ключ", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 29, "category": "nowpayments", "key": "api_url", "value": "https://api.nowpayments.io/v1", "type": "string", "description": "API URL", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 30, "category": "nowpayments", "key": "test_mode", "value": "0", "type": "boolean", "description": "Тестовый режим", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 31, "category": "nowpayments", "key": "email", "value": "<EMAIL>", "type": "string", "description": "Email аккаунта NOWPayments", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 32, "category": "nowpayments", "key": "password", "value": "Yjen10,er20", "type": "string", "description": "Пароль аккаунта NOWPayments", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 33, "category": "nowpayments", "key": "min_withdrawal_amount", "value": "100", "type": "number", "description": "Минимальная сумма для вывода", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 34, "category": "nowpayments", "key": "min_balance_for_withdrawal", "value": "1", "type": "number", "description": "Минимальный баланс для доступа к выводу", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 35, "category": "nowpayments", "key": "conversion_rate", "value": "0.001", "type": "number", "description": "Курс конвертации монет в USD", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 36, "category": "nowpayments", "key": "show_fees_to_user", "value": "1", "type": "boolean", "description": "Показывать комиссии пользователю", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 37, "category": "nowpayments", "key": "allow_invalid_hash", "value": "0", "type": "boolean", "description": "Разрешить невалидные хеши", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 38, "category": "nowpayments", "key": "api_auth_enabled", "value": "1", "type": "boolean", "description": "Включить авторизацию API", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}, {"id": 39, "category": "nowpayments", "key": "api_auth_token", "value": "secretnii-test", "type": "string", "description": "Токен для авторизации API", "created_at": "2025-07-21 10:43:46", "updated_at": "2025-07-21 10:43:46"}]