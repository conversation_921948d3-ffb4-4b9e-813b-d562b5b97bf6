<?php
echo "=== ИСПРАВЛЕНИЕ PHP.INI ===\n\n";

$phpIniPath = 'D:\OSPanel\modules\php\PHP_8.1\php.ini';
$extPath = 'D:\OSPanel\modules\php\PHP_8.1\ext';

echo "Исправляем php.ini: $phpIniPath\n";
echo "Папка расширений: $extPath\n\n";

// Читаем php.ini
$content = file_get_contents($phpIniPath);
if (!$content) {
    echo "❌ Ошибка чтения php.ini\n";
    exit(1);
}

echo "✅ php.ini прочитан (" . strlen($content) . " байт)\n";

// Создаем резервную копию
$backupPath = $phpIniPath . '.backup_' . date('Y-m-d_H-i-s');
if (copy($phpIniPath, $backupPath)) {
    echo "✅ Создана резервная копия: $backupPath\n";
} else {
    echo "❌ Ошибка создания резервной копии\n";
    exit(1);
}

// Исправления
$fixes = [
    // Исправляем путь к расширениям
    'sqlite3.extension_dir = C:\php\ext' => 'sqlite3.extension_dir = D:\OSPanel\modules\php\PHP_8.1\ext',
    
    // Убеждаемся, что расширения включены
    ';extension=pdo_sqlite' => 'extension=pdo_sqlite',
    ';extension=sqlite3' => 'extension=sqlite3',
    
    // Исправляем extension_dir
    'extension_dir = "ext"' => 'extension_dir = "D:\OSPanel\modules\php\PHP_8.1\ext"',
    ';extension_dir = "ext"' => 'extension_dir = "D:\OSPanel\modules\php\PHP_8.1\ext"',
];

$changes = 0;
foreach ($fixes as $search => $replace) {
    if (strpos($content, $search) !== false) {
        $content = str_replace($search, $replace, $content);
        $changes++;
        echo "✅ Исправлено: $search -> $replace\n";
    }
}

// Дополнительные проверки и исправления
echo "\n🔧 ДОПОЛНИТЕЛЬНЫЕ ИСПРАВЛЕНИЯ:\n";

// Убеждаемся, что extension_dir установлен правильно
if (strpos($content, 'extension_dir = "D:\OSPanel\modules\php\PHP_8.1\ext"') === false) {
    // Ищем строку extension_dir и заменяем
    $content = preg_replace('/^extension_dir\s*=.*$/m', 'extension_dir = "D:\OSPanel\modules\php\PHP_8.1\ext"', $content);
    echo "✅ Установлен extension_dir\n";
    $changes++;
}

// Убеждаемся, что SQLite расширения включены
$sqliteExtensions = ['pdo_sqlite', 'sqlite3'];
foreach ($sqliteExtensions as $ext) {
    if (strpos($content, "extension=$ext") === false) {
        // Добавляем расширение в секцию Dynamic Extensions
        $dynamicSection = strpos($content, '; Dynamic Extensions ;');
        if ($dynamicSection !== false) {
            $insertPos = strpos($content, "\n", $dynamicSection) + 1;
            $content = substr_replace($content, "extension=$ext\n", $insertPos, 0);
            echo "✅ Добавлено расширение: $ext\n";
            $changes++;
        }
    }
}

// Сохраняем исправленный php.ini
if (file_put_contents($phpIniPath, $content)) {
    echo "\n✅ php.ini сохранен с $changes изменениями\n";
} else {
    echo "\n❌ Ошибка сохранения php.ini\n";
    exit(1);
}

// Проверяем файлы расширений
echo "\n📁 ПРОВЕРКА ФАЙЛОВ РАСШИРЕНИЙ:\n";
$requiredFiles = [
    'php_pdo_sqlite.dll',
    'php_sqlite3.dll'
];

foreach ($requiredFiles as $file) {
    $filePath = "$extPath\\$file";
    if (file_exists($filePath)) {
        $size = filesize($filePath);
        $date = date('Y-m-d H:i:s', filemtime($filePath));
        echo "✅ $file ($size байт, $date)\n";
    } else {
        echo "❌ $file НЕ НАЙДЕН!\n";
    }
}

echo "\n🎯 РЕЗУЛЬТАТ:\n";
echo "✅ php.ini исправлен\n";
echo "✅ SQLite расширения настроены\n";
echo "✅ Пути к расширениям исправлены\n";
echo "\n⚠️  ПЕРЕЗАПУСТИТЕ ВЕБА-СЕРВЕР для применения изменений!\n";

echo "\n=== КОНЕЦ ИСПРАВЛЕНИЯ ===\n";
?>
