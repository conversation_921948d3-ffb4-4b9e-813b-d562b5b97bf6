<?php
// Поиск OSPanel PHP
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Поиск OSPanel PHP</h1>";

echo "<h2>📊 Текущая информация PHP</h2>";
echo "<p>PHP версия: " . PHP_VERSION . "</p>";
echo "<p>PHP SAPI: " . php_sapi_name() . "</p>";
echo "<p>PHP Binary: " . (defined('PHP_BINARY') ? PHP_BINARY : 'НЕ ОПРЕДЕЛЕНО') . "</p>";
echo "<p>PHP.ini файл: " . php_ini_loaded_file() . "</p>";

// Получаем информацию о расширениях
echo "<h2>🔧 Загруженные расширения</h2>";
$extensions = get_loaded_extensions();
echo "<p>Всего расширений: " . count($extensions) . "</p>";

$sqliteExtensions = array_filter($extensions, function($ext) {
    return stripos($ext, 'sqlite') !== false || stripos($ext, 'pdo') !== false;
});

if ($sqliteExtensions) {
    echo "<p>SQLite/PDO расширения: " . implode(', ', $sqliteExtensions) . "</p>";
} else {
    echo "<p style='color: red'>❌ SQLite расширения не найдены</p>";
}

// Ищем возможные пути OSPanel
echo "<h2>📁 Поиск OSPanel</h2>";

$possiblePaths = [
    'D:\OSPanel',
    'C:\OSPanel', 
    'D:\OpenServer',
    'C:\OpenServer'
];

$foundOSPanel = null;

foreach ($possiblePaths as $path) {
    if (is_dir($path)) {
        echo "<p>✅ Найден: $path</p>";
        $foundOSPanel = $path;
        break;
    } else {
        echo "<p>❌ Не найден: $path</p>";
    }
}

if ($foundOSPanel) {
    echo "<h3>🔍 Поиск PHP в OSPanel</h3>";
    
    $phpModulesPath = $foundOSPanel . '\modules\php';
    if (is_dir($phpModulesPath)) {
        echo "<p>✅ Папка PHP модулей: $phpModulesPath</p>";
        
        $phpVersions = glob($phpModulesPath . '\PHP_*');
        echo "<p>Найденные версии PHP:</p>";
        
        foreach ($phpVersions as $phpVersion) {
            $versionName = basename($phpVersion);
            $phpIniPath = $phpVersion . '\php.ini';
            $extPath = $phpVersion . '\ext';
            
            echo "<p><strong>$versionName:</strong></p>";
            echo "<p>&nbsp;&nbsp;php.ini: " . (file_exists($phpIniPath) ? "✅" : "❌") . "</p>";
            echo "<p>&nbsp;&nbsp;ext папка: " . (is_dir($extPath) ? "✅" : "❌") . "</p>";
            
            if (is_dir($extPath)) {
                $sqliteFiles = [
                    'php_pdo_sqlite.dll',
                    'php_sqlite3.dll'
                ];
                
                foreach ($sqliteFiles as $file) {
                    $filePath = $extPath . '\\' . $file;
                    echo "<p>&nbsp;&nbsp;$file: " . (file_exists($filePath) ? "✅" : "❌") . "</p>";
                }
            }
        }
        
        // Определяем активную версию
        echo "<h3>🎯 Определение активной версии</h3>";
        
        // Проверяем через phpinfo
        ob_start();
        phpinfo(INFO_GENERAL);
        $phpinfo = ob_get_clean();
        
        if (preg_match('/Configuration File \(php\.ini\) Path.*?([A-Z]:[^<]+)/i', $phpinfo, $matches)) {
            $configPath = trim($matches[1]);
            echo "<p>Путь конфигурации из phpinfo: $configPath</p>";
        }
        
        if (preg_match('/Loaded Configuration File.*?([A-Z]:[^<]+php\.ini)/i', $phpinfo, $matches)) {
            $loadedConfig = trim($matches[1]);
            echo "<p>Загруженный php.ini: $loadedConfig</p>";
            
            // Определяем версию PHP из пути
            if (preg_match('/PHP_(\d+\.\d+)/', $loadedConfig, $versionMatch)) {
                $activeVersion = 'PHP_' . $versionMatch[1];
                echo "<p><strong>🎯 Активная версия: $activeVersion</strong></p>";
                
                // Показываем кнопку для исправления
                echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>🔧 Исправление SQLite</h4>";
                echo "<p>Найдена активная версия PHP: $activeVersion</p>";
                echo "<p>Путь к php.ini: $loadedConfig</p>";
                echo "<form method='post' style='margin: 10px 0;'>";
                echo "<input type='hidden' name='action' value='fix_sqlite'>";
                echo "<input type='hidden' name='php_version' value='$activeVersion'>";
                echo "<input type='hidden' name='php_ini_path' value='$loadedConfig'>";
                echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔧 Исправить SQLite в $activeVersion</button>";
                echo "</form>";
                echo "</div>";
            }
        }
    }
}

// Обработка исправления
if ($_POST['action'] ?? '' === 'fix_sqlite') {
    $phpVersion = $_POST['php_version'];
    $phpIniPath = $_POST['php_ini_path'];
    
    echo "<h2>🔧 Исправление SQLite в $phpVersion</h2>";
    
    if (file_exists($phpIniPath)) {
        $content = file_get_contents($phpIniPath);
        $originalContent = $content;
        
        // Создаем резервную копию
        $backupPath = $phpIniPath . '.backup_web_' . date('Y-m-d_H-i-s');
        file_put_contents($backupPath, $content);
        echo "<p>✅ Резервная копия: $backupPath</p>";
        
        $changes = 0;
        
        // Включаем расширения
        $extensions = [
            ';extension=pdo_sqlite' => 'extension=pdo_sqlite',
            ';extension=sqlite3' => 'extension=sqlite3'
        ];
        
        foreach ($extensions as $search => $replace) {
            if (strpos($content, $search) !== false) {
                $content = str_replace($search, $replace, $content);
                echo "<p>✅ Включено: " . str_replace('extension=', '', $replace) . "</p>";
                $changes++;
            }
        }
        
        // Добавляем расширения если их нет
        if (strpos($content, 'extension=pdo_sqlite') === false) {
            $content .= "\n; SQLite extensions\nextension=pdo_sqlite\n";
            $changes++;
            echo "<p>✅ Добавлено: pdo_sqlite</p>";
        }
        
        if (strpos($content, 'extension=sqlite3') === false) {
            $content .= "extension=sqlite3\n";
            $changes++;
            echo "<p>✅ Добавлено: sqlite3</p>";
        }
        
        if ($changes > 0) {
            if (file_put_contents($phpIniPath, $content)) {
                echo "<p style='color: green; font-weight: bold;'>✅ php.ini исправлен! Сделано изменений: $changes</p>";
                echo "<p style='color: orange; font-weight: bold;'>🔄 ПЕРЕЗАПУСТИТЕ OSPANEL для применения изменений!</p>";
            } else {
                echo "<p style='color: red;'>❌ Ошибка сохранения php.ini</p>";
            }
        } else {
            echo "<p>ℹ️ Расширения уже включены</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ php.ini не найден: $phpIniPath</p>";
    }
}

echo "<hr>";
echo "<p><a href='/test_sqlite_final.php'>🔄 Повторить тест SQLite</a></p>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
