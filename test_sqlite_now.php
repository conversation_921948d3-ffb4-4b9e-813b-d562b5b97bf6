<?php
// Тест SQLite в OSPanel
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧪 Тест SQLite в OSPanel</h1>";

echo "<h2>📊 Информация о PHP</h2>";
echo "<p>PHP версия: " . PHP_VERSION . "</p>";
echo "<p>Дата проверки: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>🔧 Расширения PHP</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? 'YES ✅' : 'NO ❌') . "</p>";
echo "<p>PDO SQLite: " . (extension_loaded('pdo_sqlite') ? 'YES ✅' : 'NO ❌') . "</p>";
echo "<p>SQLite3: " . (extension_loaded('sqlite3') ? 'YES ✅' : 'NO ❌') . "</p>";

if (class_exists('PDO')) {
    $drivers = PDO::getAvailableDrivers();
    echo "<p>PDO драйверы: " . implode(', ', $drivers) . "</p>";
    echo "<p>SQLite в PDO: " . (in_array('sqlite', $drivers) ? 'YES ✅' : 'NO ❌') . "</p>";
}

echo "<h2>🧪 Тест создания SQLite базы</h2>";

if (extension_loaded('pdo_sqlite')) {
    try {
        $testDb = 'test_sqlite_' . time() . '.db';
        echo "<p>Создаем тестовую базу: $testDb</p>";
        
        $pdo = new PDO("sqlite:$testDb");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ SQLite база создана успешно!</p>";
        
        $pdo->exec("CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT, created_at DATETIME DEFAULT CURRENT_TIMESTAMP)");
        echo "<p>✅ Таблица создана</p>";
        
        $pdo->exec("INSERT INTO test_table (name) VALUES ('OSPanel SQLite Test')");
        echo "<p>✅ Данные вставлены</p>";
        
        $result = $pdo->query("SELECT * FROM test_table")->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ Данные прочитаны: " . $result['name'] . "</p>";
        
        $count = $pdo->query("SELECT COUNT(*) as cnt FROM test_table")->fetchColumn();
        echo "<p>✅ Записей в таблице: $count</p>";
        
        // Удаляем тестовую базу
        unlink($testDb);
        echo "<p>✅ Тестовая база удалена</p>";
        
        echo "<h3 style='color: green;'>🎉 SQLITE РАБОТАЕТ В OSPANEL! 🎉</h3>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Ошибка SQLite: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ PDO SQLite расширение не загружено</p>";
}

echo "<h2>🔍 Тест нашей базы данных</h2>";

if (extension_loaded('pdo_sqlite')) {
    try {
        $ourDb = 'database/app.sqlite';
        echo "<p>Проверяем нашу базу: $ourDb</p>";
        echo "<p>Файл существует: " . (file_exists($ourDb) ? 'ДА' : 'НЕТ') . "</p>";
        echo "<p>Размер файла: " . filesize($ourDb) . " байт</p>";
        
        $pdo = new PDO("sqlite:$ourDb");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ Подключение к нашей базе успешно!</p>";
        
        // Проверяем таблицы
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>Таблицы в базе: " . implode(', ', $tables) . "</p>";
        
        // Проверяем данные в каждой таблице
        foreach ($tables as $table) {
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                echo "<p>$table: $count записей</p>";
                
                if ($count > 0 && $table === 'settings') {
                    $examples = $pdo->query("SELECT category, key, value FROM settings LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
                    echo "<p>Примеры настроек:</p>";
                    foreach ($examples as $setting) {
                        echo "<p>&nbsp;&nbsp;- {$setting['category']}.{$setting['key']} = " . substr($setting['value'], 0, 30) . "</p>";
                    }
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Ошибка чтения $table: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Ошибка подключения к нашей базе: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ SQLite не доступен</p>";
}

echo "<h2>🎯 Результат</h2>";
if (extension_loaded('pdo_sqlite')) {
    echo "<p style='color: green; font-weight: bold;'>✅ SQLITE РАБОТАЕТ В OSPANEL!</p>";
    echo "<p>Теперь нужно исправить наш Database класс, чтобы он правильно использовал SQLite.</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ SQLITE НЕ РАБОТАЕТ В OSANEL</p>";
    echo "<p>Нужно исправить конфигурацию SQLite в OSPanel.</p>";
}

echo "<hr>";
echo "<p><a href='/admin/'>← Вернуться в админку</a></p>";
?>
