<?php
/**
 * test_apply_settings.php
 * Тестирование применения настроек к боту
 */

require_once __DIR__ . '/config.php';

echo "=== ТЕСТИРОВАНИЕ ПРИМЕНЕНИЯ НАСТРОЕК К БОТУ ===\n\n";

/**
 * Тест 1: Проверка загрузки текстов
 */
echo "1. Проверка загрузки текстов:\n";

function loadBotTexts() {
    $textsFile = __DIR__ . '/bot_texts.json';
    if (!file_exists($textsFile)) {
        return false;
    }
    
    $content = file_get_contents($textsFile);
    return json_decode($content, true);
}

$texts = loadBotTexts();
if ($texts) {
    echo "✅ Тексты загружены успешно\n";
    echo "   - Языки: " . implode(', ', array_keys($texts)) . "\n";
} else {
    echo "❌ Ошибка загрузки текстов\n";
    exit(1);
}

/**
 * Тест 2: Проверка подключения к Telegram API
 */
echo "\n2. Проверка подключения к Telegram API:\n";
$botInfo = telegramRequest('getMe');
if ($botInfo) {
    echo "✅ API работает\n";
    echo "   - Бот: @{$botInfo['username']}\n";
} else {
    echo "❌ Ошибка подключения к API\n";
    exit(1);
}

/**
 * Тест 3: Тестирование setChatMenuButton
 */
echo "\n3. Тестирование setChatMenuButton:\n";

// Русский Menu Button
$textRu = $texts['ru']['bot_interface']['menu_button_text'] ?? '🚀 Запустить';
$menuButtonRu = [
    'type' => 'web_app',
    'text' => $textRu,
    'web_app' => ['url' => WEBAPP_URL]
];

echo "   Устанавливаем русский Menu Button: {$textRu}\n";
$resultRu = telegramRequest('setChatMenuButton', ['menu_button' => json_encode($menuButtonRu)]);
if ($resultRu) {
    echo "   ✅ Русский Menu Button установлен\n";
} else {
    echo "   ❌ Ошибка установки русского Menu Button\n";
}

sleep(2); // Задержка между запросами

// Английский Menu Button  
$textEn = $texts['en']['bot_interface']['menu_button_text'] ?? '🚀 Launch';
$menuButtonEn = [
    'type' => 'web_app',
    'text' => $textEn,
    'web_app' => ['url' => WEBAPP_URL]
];

echo "   Устанавливаем английский Menu Button: {$textEn}\n";
$resultEn = telegramRequest('setChatMenuButton', ['menu_button' => json_encode($menuButtonEn)]);
if ($resultEn) {
    echo "   ✅ Английский Menu Button установлен\n";
} else {
    echo "   ❌ Ошибка установки английского Menu Button\n";
}

/**
 * Тест 4: Тестирование setMyCommands
 */
echo "\n4. Тестирование setMyCommands:\n";

sleep(2); // Задержка между запросами

// Команды для русского языка
$commandsRu = [
    ['command' => 'start', 'description' => $texts['ru']['commands']['start'] ?? '🚀 Запустить приложение'],
    ['command' => 'balance', 'description' => $texts['ru']['commands']['balance'] ?? '💰 Посмотреть баланс'],
    ['command' => 'stats', 'description' => $texts['ru']['commands']['stats'] ?? '📊 Статистика'],
    ['command' => 'help', 'description' => $texts['ru']['commands']['help'] ?? '❓ Помощь']
];

echo "   Устанавливаем команды для русского языка...\n";
$resultRu = telegramRequest('setMyCommands', ['commands' => json_encode($commandsRu), 'language_code' => 'ru']);
if ($resultRu) {
    echo "   ✅ Команды для русского языка установлены\n";
} else {
    echo "   ❌ Ошибка установки команд для русского языка\n";
}

sleep(2); // Задержка между запросами

// Команды для английского языка
$commandsEn = [
    ['command' => 'start', 'description' => $texts['en']['commands']['start'] ?? '🚀 Launch app'],
    ['command' => 'balance', 'description' => $texts['en']['commands']['balance'] ?? '💰 Check balance'],
    ['command' => 'stats', 'description' => $texts['en']['commands']['stats'] ?? '📊 Statistics'],
    ['command' => 'help', 'description' => $texts['en']['commands']['help'] ?? '❓ Help']
];

echo "   Устанавливаем команды для английского языка...\n";
$resultEn = telegramRequest('setMyCommands', ['commands' => json_encode($commandsEn), 'language_code' => 'en']);
if ($resultEn) {
    echo "   ✅ Команды для английского языка установлены\n";
} else {
    echo "   ❌ Ошибка установки команд для английского языка\n";
}

sleep(2); // Задержка между запросами

// Команды по умолчанию
echo "   Устанавливаем команды по умолчанию...\n";
$resultDefault = telegramRequest('setMyCommands', ['commands' => json_encode($commandsEn)]);
if ($resultDefault) {
    echo "   ✅ Команды по умолчанию установлены\n";
} else {
    echo "   ❌ Ошибка установки команд по умолчанию\n";
}

/**
 * Тест 5: Тестирование setMyDescription
 */
echo "\n5. Тестирование setMyDescription:\n";

sleep(2); // Задержка между запросами

// Описания
$descRu = $texts['ru']['bot_interface']['description'] ?? $texts['ru']['bot_interface']['description_full'] ?? 'UniQPaid - зарабатывайте криптовалюту!';
$descEn = $texts['en']['bot_interface']['description'] ?? $texts['en']['bot_interface']['description_full'] ?? 'UniQPaid - earn cryptocurrency!';

echo "   Устанавливаем описание для русского языка...\n";
$resultDescRu = telegramRequest('setMyDescription', ['description' => $descRu, 'language_code' => 'ru']);
if ($resultDescRu) {
    echo "   ✅ Описание для русского языка установлено\n";
} else {
    echo "   ❌ Ошибка установки описания для русского языка\n";
}

sleep(2); // Задержка между запросами

echo "   Устанавливаем описание для английского языка...\n";
$resultDescEn = telegramRequest('setMyDescription', ['description' => $descEn, 'language_code' => 'en']);
if ($resultDescEn) {
    echo "   ✅ Описание для английского языка установлено\n";
} else {
    echo "   ❌ Ошибка установки описания для английского языка\n";
}

sleep(2); // Задержка между запросами

echo "   Устанавливаем описание по умолчанию...\n";
$resultDescDefault = telegramRequest('setMyDescription', ['description' => $descEn]);
if ($resultDescDefault) {
    echo "   ✅ Описание по умолчанию установлено\n";
} else {
    echo "   ❌ Ошибка установки описания по умолчанию\n";
}

/**
 * Тест 6: Тестирование setMyShortDescription
 */
echo "\n6. Тестирование setMyShortDescription:\n";

sleep(2); // Задержка между запросами

$shortDescRu = $texts['ru']['bot_interface']['description_short'] ?? 'Зарабатывайте криптовалюту!';
$shortDescEn = $texts['en']['bot_interface']['description_short'] ?? 'Earn cryptocurrency!';

echo "   Устанавливаем короткое описание для русского языка...\n";
$resultShortRu = telegramRequest('setMyShortDescription', ['short_description' => $shortDescRu, 'language_code' => 'ru']);
if ($resultShortRu) {
    echo "   ✅ Короткое описание для русского языка установлено\n";
} else {
    echo "   ❌ Ошибка установки короткого описания для русского языка\n";
}

sleep(2); // Задержка между запросами

echo "   Устанавливаем короткое описание для английского языка...\n";
$resultShortEn = telegramRequest('setMyShortDescription', ['short_description' => $shortDescEn, 'language_code' => 'en']);
if ($resultShortEn) {
    echo "   ✅ Короткое описание для английского языка установлено\n";
} else {
    echo "   ❌ Ошибка установки короткого описания для английского языка\n";
}

sleep(2); // Задержка между запросами

echo "   Устанавливаем короткое описание по умолчанию...\n";
$resultShortDefault = telegramRequest('setMyShortDescription', ['short_description' => $shortDescEn]);
if ($resultShortDefault) {
    echo "   ✅ Короткое описание по умолчанию установлено\n";
} else {
    echo "   ❌ Ошибка установки короткого описания по умолчанию\n";
}

echo "\n=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";
echo "Проверьте бота в Telegram для подтверждения изменений.\n";
?>
